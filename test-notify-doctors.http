# Test notification by product
POST http://localhost:3000/product/notify-doctors
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "filterType": "product",
  "productId": "some-product-id",
  "message": "Test notification for product"
}

###

# Test notification by state
POST http://localhost:3000/product/notify-doctors
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "filterType": "state",
  "stateIds": ["state-id-1", "state-id-2"],
  "message": "Test notification for states"
}

###

# Test notification by doctor IDs
POST http://localhost:3000/product/notify-doctors
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "filterType": "doctorIds",
  "doctorIds": ["doctor-id-1", "doctor-id-2"],
  "message": "Test notification for specific doctors"
}