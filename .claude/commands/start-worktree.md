# Start a new worktree from a ticket

1. Get the ClickUp URL from $ARGUMENTS, get the title, description and, if exists, the sub-tickets. If no ClickUp URL is provided,
   prompt for it
2. Generate a new branch name from `origin/staging` using: `{custom-id}-{ticket-title-in-kebab-case}`, keep it short
3. Write the description of the ticket in `resources/tickets/{custom-id}.md`
4. Run `pnpm worktree:setup --branch {generated-branch-name} --name {name}` to create a new worktree for the ticket
   implementation (name should comply with `/^[a-zA-Z0-9/_-]{1,14}$/`, use 1 or 2 simple words if possible). This
   command takes time to run (1 or 2 mins), so be patient.
5. after the worktree is created, YOU ARE DONE
