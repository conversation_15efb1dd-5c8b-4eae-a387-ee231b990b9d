import * as pulumi from '@pulumi/pulumi';

import { vpc } from './network';

export { projectName } from './config';

// Configuration
const config = new pulumi.Config();

export const publicSubnet = vpc.publicSubnetIds[0];
export const privateSubnet = vpc.privateSubnetIds[0];

export let metabaseUrl: pulumi.Output<string> = pulumi.output('');
if (config.get('deployMetabase') === 'Yes') {
  const metabase = await import('./metabase');
  metabaseUrl = metabase.metabaseUrl;
}
