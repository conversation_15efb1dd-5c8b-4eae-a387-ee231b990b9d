import * as awsx from '@pulumi/awsx';

import { defaultTags, envName, projectName } from './config';

// Create VPC
export const vpc = new awsx.ec2.Vpc('vpc', {
  cidrBlock: '10.0.0.0/16',
  numberOfAvailabilityZones: 2,
  enableDnsHostnames: true,
  enableDnsSupport: true,
  natGateways: {
    strategy: awsx.ec2.NatGatewayStrategy.Single,
  },
  tags: {
    Name: `${projectName}-${envName}-vpc`,
    ...defaultTags,
  },
});
