import * as aws from '@pulumi/aws';
import * as awsx from '@pulumi/awsx';
import * as pulumi from '@pulumi/pulumi';
import * as random from '@pulumi/random';

import { defaultTags, envName, projectName } from './config';
import { vpc } from './network';

// Configuration
const config = new pulumi.Config();

const serviceName = `${projectName}-${envName}-metabase`;

const databaseUsername = config.get('metabaseDatabaseUsername') || 'postgres';
const databaseName = config.get('metabaseDatabaseName') || 'postgres';
// Generate a random password if none is provided
const dbPasswordFromConfig = config.getSecret('metabaseDatabasePassword');
const generatedPassword = new random.RandomPassword('metabase-db-password', {
  length: 16,
  special: true,
  upper: true,
  lower: true,
  numeric: true,
});

const databasePassword = dbPasswordFromConfig || generatedPassword.result;

const metabaseEncryptionSecretGenerator = new random.RandomPassword(
  'metabase-encryption-secret',
  {
    length: 16,
    special: true,
    upper: true,
    lower: true,
    numeric: true,
  },
);

const metabaseEncryptionSecretKey =
  config.get('metabaseEncryptionSecretKey') ||
  metabaseEncryptionSecretGenerator.result;

////////////
/// Database
const securityGroup = new aws.ec2.SecurityGroup('metabase-service-sg', {
  vpcId: vpc.vpcId,
  egress: [
    {
      fromPort: 0,
      toPort: 0,
      protocol: '-1',
      cidrBlocks: ['0.0.0.0/0'],
      ipv6CidrBlocks: ['::/0'],
    },
  ],
  ingress: [
    {
      protocol: '-1',
      fromPort: 0,
      toPort: 0,
      cidrBlocks: ['0.0.0.0/0'],
      ipv6CidrBlocks: ['::/0'],
    },
  ],
  tags: {
    Service: serviceName,
    Name: `${projectName}-${envName}-metabase-sg`,
    ...defaultTags,
  },
});

// Database security group - updated to allow access from VM
const dbSecurityGroup = new aws.ec2.SecurityGroup('metabase-db-sg', {
  vpcId: vpc.vpcId,
  description: 'Security group for Aurora PostgreSQL database',
  ingress: [
    {
      fromPort: 5432,
      toPort: 5432,
      protocol: 'tcp',
      securityGroups: [securityGroup.id],
      description: 'Access from Metabase service',
    },
  ],
  tags: {
    Service: serviceName,
    Name: `${projectName}-${envName}-db-sg`,
    ...defaultTags,
  },
});

// Create Aurora Serverless PostgreSQL cluster
export const pgCluster = new aws.rds.Cluster('metabase-pg-cluster', {
  clusterIdentifier: `${projectName}-${envName}-metabase-pg-cluster`,
  engine: aws.rds.EngineType.AuroraPostgresql,
  engineMode: aws.rds.EngineMode.Provisioned, // serverlessV2
  engineVersion: '17.5',
  serverlessv2ScalingConfiguration: {
    maxCapacity: 1,
    minCapacity: 0,
    secondsUntilAutoPause: 3600,
  },
  databaseName: databaseName,
  masterUsername: databaseUsername,
  masterPassword: databasePassword,
  vpcSecurityGroupIds: [dbSecurityGroup.id],
  skipFinalSnapshot: true,
  dbSubnetGroupName: new aws.rds.SubnetGroup('metabase-db-subnet-group', {
    name: `${projectName}-${envName}-metabase-db-subnet-group`,
    subnetIds: vpc.privateSubnetIds,
    tags: {
      Name: `${projectName}-${envName}-metabase-db-subnet-group`,
      ...defaultTags,
    },
  }).name,
  tags: {
    Service: serviceName,
    Name: `${projectName}-${envName}-metabase-pg-cluster`,
    ...defaultTags,
  },
});

const pgClusterInstance = new aws.rds.ClusterInstance(
  'metabase-pg-cluster-instance',
  {
    clusterIdentifier: pgCluster.id,
    instanceClass: 'db.serverless',
    engine: aws.rds.EngineType.AuroraPostgresql,
    engineVersion: pgCluster.engineVersion,
    tags: {
      Service: serviceName,
      Name: `${projectName}-${envName}-metabase-pg-cluster-instance`,
      ...defaultTags,
    },
  },
);

// Create AWS Secrets Manager secret for database url
export const metabasDbUrlSecret = new aws.secretsmanager.Secret(
  'metabase-secrets',
  {
    name: `${projectName}-${envName}-metabase-secrets`,
    description: 'Secrets for Metabase',
    tags: {
      Service: serviceName,
      Name: `${projectName}-${envName}-metabase-secrets`,
      ...defaultTags,
    },
  },
);

const dbUrlSecretVersion = new aws.secretsmanager.SecretVersion(
  'metabase-secrets-version',
  {
    secretId: metabasDbUrlSecret.id,
    secretString: pulumi
      .all([pgCluster, metabaseEncryptionSecretKey])
      .apply(([pgCluster, metabaseEncryptionSecretKey]) =>
        pulumi.jsonStringify({
          POSTGRES_URL: pulumi.interpolate`postgresql://${databaseUsername}:${databasePassword}@${pgCluster.endpoint}:${pgCluster.port}/${databaseName}?sslmode=require`,
          METABASE_ENCRYPTION_SECRET_KEY: metabaseEncryptionSecretKey,
        }),
      ),
  },
);

////////////
/// ECS and Load Balancer

// Create Application Load Balancer in public subnets
const metabaseLb = new awsx.lb.ApplicationLoadBalancer('metabase-lb', {
  name: `${projectName}-${envName}-metabase-lb`,
  subnetIds: vpc.publicSubnetIds,
  defaultTargetGroup: {
    protocol: 'HTTP',
    port: 3000,
    healthCheck: {
      healthyThreshold: 2,
      unhealthyThreshold: 10,
      timeout: 10,
      interval: 60,
      path: '/api/health',
      matcher: '200',
    },
  },
  tags: {
    Service: serviceName,
    Name: `${projectName}-${envName}-metabase-lb`,
    ...defaultTags,
  },
});

// Create ECS Cluster
const cluster = new aws.ecs.Cluster('metabase-ecs-cluster', {
  name: `${projectName}-${envName}-metabase-ecs-cluster`,
  tags: {
    Service: serviceName,
    Name: `${projectName}-${envName}-metabase-ecs-cluster`,
    ...defaultTags,
  },
});

// Create IAM role for ECS task execution
const ecsTaskExecutionRole = new aws.iam.Role(
  'metabase-ecs-task-execution-role',
  {
    assumeRolePolicy: JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'sts:AssumeRole',
          Effect: 'Allow',
          Principal: {
            Service: 'ecs-tasks.amazonaws.com',
          },
        },
      ],
    }),
    tags: {
      Service: serviceName,
      Name: `${projectName}-${envName}-metabase-ecs-task-execution-role`,
      ...defaultTags,
    },
  },
);

// Attach the default ECS task execution role policy
const ecsTaskExecutionRolePolicy = new aws.iam.RolePolicyAttachment(
  'ecs-task-execution-role-policy',
  {
    role: ecsTaskExecutionRole.name,
    policyArn:
      'arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy',
  },
);

// Create custom policy for accessing secrets
const secretsAccessPolicy = new aws.iam.Policy(
  'metabase-secrets-access-policy',
  {
    description: 'Policy for accessing database secrets',
    policy: pulumi.all([metabasDbUrlSecret.arn]).apply(([urlArn]) =>
      JSON.stringify({
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Action: ['secretsmanager:GetSecretValue'],
            Resource: [urlArn],
          },
        ],
      }),
    ),
    tags: {
      Service: serviceName,
      Name: `${projectName}-${envName}-metabase-secrets-access-policy`,
      ...defaultTags,
    },
  },
);

// Attach the custom policy to the execution role
const secretsAccessPolicyAttachment = new aws.iam.RolePolicyAttachment(
  'metabase-secrets-access-policy-attachment',
  {
    role: ecsTaskExecutionRole.name,
    policyArn: secretsAccessPolicy.arn,
  },
);

export const metabaseService = new awsx.ecs.FargateService('metabase-service', {
  name: serviceName,
  cluster: cluster.arn,
  networkConfiguration: {
    subnets: vpc.privateSubnetIds,
    securityGroups: [securityGroup.id],
  },
  desiredCount: 1,
  forceNewDeployment: true,
  taskDefinitionArgs: {
    family: `${projectName}-${envName}-metabase`,
    executionRole: {
      roleArn: ecsTaskExecutionRole.arn,
    },
    container: {
      name: `${projectName}-${envName}-metabase`,
      image: 'metabase/metabase:v0.55.8.3',
      cpu: 1024,
      memory: 2048,
      essential: true,
      environment: [
        {
          name: 'JAVA_OPTS',
          value: '-Xmx2g',
        },
        {
          name: 'MB_JETTY_PORT',
          value: '3000',
        },
        {
          name: 'MB_DB_TYPE',
          value: 'postgres',
        },
      ],
      secrets: [
        {
          name: 'MB_DB_CONNECTION_URI',
          valueFrom: pulumi.interpolate`${metabasDbUrlSecret.arn}:POSTGRES_URL::`,
        },
        {
          name: 'MB_ENCRYPTION_SECRET_KEY',
          valueFrom: pulumi.interpolate`${metabasDbUrlSecret.arn}:METABASE_ENCRYPTION_SECRET_KEY::`,
        },
      ],
      portMappings: [
        {
          containerPort: 3000,
          targetGroup: metabaseLb.defaultTargetGroup,
        },
      ],
    },
    tags: {
      Service: serviceName,
      Name: `${projectName}-${envName}-metabase-service`,
      ...defaultTags,
    },
  },
});

export const metabaseUrl = pulumi.interpolate`http://${metabaseLb.loadBalancer.dnsName}`;
