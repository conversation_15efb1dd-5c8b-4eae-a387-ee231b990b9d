{"name": "@willow/infrastructure", "private": true, "version": "0.1.0", "description": "Pulumi IaC for Willow", "type": "module", "main": "src/index.ts", "scripts": {"pulumi": "pulumi", "deploy:staging": "pulumi up --stack staging", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^22.5.0", "@willow/eslint-config": "workspace:*", "@willow/prettier-config": "workspace:*", "@willow/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "dependencies": {"@pulumi/aws": "^6.83.0", "@pulumi/awsx": "^2.22.0", "@pulumi/pulumi": "^3.181.0", "@pulumi/random": "^4.18.2"}}