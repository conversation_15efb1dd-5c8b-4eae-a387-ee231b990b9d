# Sample Pulumi configuration values
# Copy this to Pulumi.dev.yaml or Pulumi.prod.yaml based on your stack

name: willow-infrastructure
description: Pulumi shared infrastructure for Willow
runtime:
  name: nodejs
  options:
    # esm support
    nodeargs: "--loader ts-node/esm --no-warnings --experimental-specifier-resolution=node"

backend:
  url: s3://willow-pulumi

config:
  # AWS region (already configured)
  aws:region: us-east-1
