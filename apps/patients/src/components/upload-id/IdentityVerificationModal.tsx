'use client';

import { InfoDialog } from '../ui/infoDialog';

export function IdentityVerificationModal({
  onClose,
}: {
  onClose: () => void;
}) {
  return (
    <InfoDialog
      title="Identity Verification"
      confirmBtnText="I understand"
      confirmBtnClick={() => onClose()}
      variant="dark"
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <span className="text-[16px] text-white md:text-xl">
            Why do you need a photo of my ID?
          </span>

          <span className="text-[13px] text-white md:text-sm">
            We use your ID photo to verify your identity, for example your name
            and date of birth. Telemedicine laws require us to verify this
            information before prescribing medication.
          </span>
        </div>

        <div className="flex flex-col gap-2">
          <span className="text-[16px] text-white md:text-xl">
            Who sees this?
          </span>

          <span className="text-[13px] text-white md:text-sm">
            The photo of your ID will be stored securely and will only be shared
            with the patient support team and our identity verification
            platform.
          </span>
        </div>
      </div>
    </InfoDialog>
  );
}
