import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import { useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import { useOnboardingService } from '@/hooks/useOnboardingService';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

interface SimpleInfoPageConfig {
  title: string;
  subtitle?: string;
  buttonText?: string;
  useOnboardingTitle?: boolean;
  onBeforeSubmit?: () => Promise<void> | void;
}

interface SimpleInfoPageProps extends OnboardingProps {
  config: SimpleInfoPageConfig;
}

const schema = z.object({});
type FormType = z.infer<typeof schema>;

const SimpleInfoPage = ({ callback, config }: SimpleInfoPageProps) => {
  const {
    title,
    subtitle,
    buttonText = 'CONTINUE',
    useOnboardingTitle = true,
    onBeforeSubmit,
  } = config;

  const [isLoading, setIsLoading] = useState(false);
  const form = useForm<FormType>({
    resolver: zodResolver(schema),
  });

  const { nextQuestionnaire, isLoading: isTransitioning } =
    useOnboardingService();

  const onSubmit = async () => {
    try {
      setIsLoading(true);
      if (onBeforeSubmit) {
        await onBeforeSubmit();
      }
      const response = await nextQuestionnaire();
      callback(response.data);
    } catch (e: any) {
      setIsLoading(false);
    }
  };

  const content = (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
        <FormLoader isLoading={isLoading || isTransitioning}>
          <Button
            size="lg"
            variant="electric"
            className="flex w-full max-w-none justify-between"
            type="submit"
            disabled={isTransitioning}
          >
            <span>{buttonText}</span>
            <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
          </Button>
        </FormLoader>
      </form>
    </Form>
  );

  if (useOnboardingTitle) {
    return (
      <OnboardingTitle title={title} subtitle={subtitle}>
        {content}
      </OnboardingTitle>
    );
  }

  return (
    <div className="flex flex-col gap-10">
      <div className="w-full text-5xl font-medium leading-[52px] text-white md:text-center">
        {title}
      </div>
      {subtitle && (
        <div className="text-lg text-white opacity-80">{subtitle}</div>
      )}
      {content}
    </div>
  );
};

export default SimpleInfoPage;
