'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useAnalytics } from '@/context/AnalyticsContext';

const questionsMap: Record<string, string> = {
  selectTreatmentType: 'Our doctors recommend these treatment options',
  selectTreatment: 'Our recommendation for you',
  identityVerification:
    'Help us verify your identity so a doctor can legally prescribe you medication',
  ssnCheck: 'Please provide the last 4 digits of your social security number',
  uploadIDPhoto: 'Upload a photo of your ID',
  uploadFacePhoto: 'Upload a photo of your face',
  visitCompletion:
    'Your Willow doctor visit is free and includes unlimited follow-ups',
  summary: 'Your visit summary',
  shipping: 'Shipping information & address',
  payment: 'Your doctor is waiting',
  onboarded: 'Success! Your visit has been sent to a Willow doctor',
  age: 'What is your birthday?',
  gender: 'What was the sex assigned to you at birth?',
  isPregnant: 'Are you pregnant, lactating, or trying to get pregnant?',
  usingGLP1: 'Are you currently using a GLP-1 medication?',
  haveDiabetes: 'Do you have diabetes?',
  doctorVisits: 'Do you have regular doctor visits?',
  qualifyingConditions: 'Do you have any of the following medical conditions?',
  height: 'What is your height?',
  weight: 'What is your weight?',
  desiredWeight: 'What would you like your weight to be?',
  haveAllergies: 'Do you have any allergies?',
  selectAllergies:
    'What are you allergic to, and what was the allergic reaction?',
  medications: 'What medications are you currently on?',
  medicalConditions: 'What medical conditions do you currently have?',
  objectives: 'Which of the following apply to you?',
  additionalInformation:
    'Is there anything else you would like the doctor to know?',
  eligible: 'Great! You may be eligible for treatment',
};

/**
 * triggers the event 'onboardingScreenViewed'
 * @param question
 */
export const useOnboardingScreenTracking = (onboardingState: string) => {
  const analytics = useAnalytics();
  const pathname = usePathname();

  useEffect(() => {
    if (!analytics) return;
    if (!onboardingState) return;
    if (!questionsMap[onboardingState]) return;
    console.log('🟡 useOnboardingScreenTracking invocation', onboardingState);
    void analytics.track('onboardingScreenViewed', {
      Question: questionsMap[onboardingState],
      currentURL: pathname,
    });
  }, [onboardingState, analytics, pathname]);
};
