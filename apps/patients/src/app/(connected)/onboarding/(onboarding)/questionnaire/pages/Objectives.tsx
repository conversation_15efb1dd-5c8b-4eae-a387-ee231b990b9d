import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import CheckboxListQuestion from '@/components/onboarding/CheckboxListQuestion';

const Objectives = (props: OnboardingProps) => {
  const config = {
    fieldName: 'objectives',
    title: 'Which of the following apply to you?',
    subtitle: `<PERSON> exclusively offers personalized treatments tailored to a patient's unique needs.`,
    options: [
      {
        id: 'loseFatWithoutLosingMuscle',
        label: 'I want to lose fat without losing muscle',
      },
      {
        id: 'decreaseFatigueIncreaseEnergy',
        label: 'I want to decrease fatigue and increase my energy',
      },
      {
        id: 'supportHeartHealth',
        label: "I'm interested in supporting my heart health",
      },
      {
        id: 'improveSkinLookAndFeel',
        label: "I'd like to improve the look and feel of my skin",
      },
      { id: 'dosingConcerns', label: "I'm concerned about dosing correctly" },
      {
        id: 'noRefrigerationNeeded',
        label: "I need medication that doesn't require refrigeration",
      },
      { id: 'travelFriendly', label: "I'd like something travel-friendly" },
    ],
    noneOption: { id: 'none', label: 'No, none of these apply to me' },
    getContextValue: (context: any) => context.questionnaire.objectives,
  };

  return <CheckboxListQuestion {...props} config={config} />;
};

export default Objectives;
