import Image from 'next/image';
import Link from 'next/link';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';

const RejectedMedicalNecessity = () => {
  return (
    <OnboardingTitle
      title="Willow is only able to provide service to patients requiring personalized treatment."
      subtitle="We recommend consulting with your doctor on if a standard brand-name prescription is right for you."
    >
      <Link href="https://www.startwillow.com/">
        <Button
          size={'lg'}
          variant={'electric'}
          className="flex w-full max-w-none justify-between"
        >
          <span>RETURN TO HOMEPAGE</span>
          <Image
            alt="arrow"
            src={arrow}
            style={{ objectFit: 'contain' }}
            className="pb-1"
          />
        </Button>
      </Link>
    </OnboardingTitle>
  );
};

export default RejectedMedicalNecessity;
