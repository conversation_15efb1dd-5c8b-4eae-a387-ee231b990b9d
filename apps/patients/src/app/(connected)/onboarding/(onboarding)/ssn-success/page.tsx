'use client';

import { useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useOnboardingNext } from '@/hooks/onboarding';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { useForm } from 'react-hook-form';

const SSNSuccess = () => {
  const analyticsData = useAnalyticsData();
  const analytics = useAnalytics();
  const form = useForm();
  const { mutateAsync: next, isPending: isFormPending } = useOnboardingNext();
  const [isLoading, setIsLoading] = useState(false);

  const { onboarding, nextOnboardingStep } = useOnboardingNavigation();

  const onSubmit = async () => {
    try {
      setIsLoading(true);
      void analytics?.track('SSN Success Continue', analyticsData);
      const response = await next({});
      nextOnboardingStep(response.data);
    } catch (e: any) {
      setIsLoading(false);
      form.setError('unsure', { message: e.response?.data?.message });
    }
  };

  if (!onboarding) return null;

  return (
    <OnboardingTitle
      title="Well done! You've completed sharing your information"
      subtitle="Before proceeding to your order, here's some vital information for you:"
    >
      <div className="space-y-6 text-white">
        <div className="flex items-start gap-3">
          <span className="text-2xl font-bold">1.</span>
          <span className="text-xl">
            The doctor may approve your medication same day after reviewing your
            virtual visit—no call needed.
          </span>
        </div>

        <div className="flex items-start gap-3">
          <span className="text-2xl font-bold">2.</span>
          <span className="text-xl">
            Prescriptions will be provided for safe treatments with no concerns
            raised.
          </span>
        </div>

        <div className="flex items-start gap-3">
          <span className="text-2xl font-bold">3.</span>
          <span className="text-xl">
            You will be able to message your doctor directly from the patient
            portal after onboarding.
          </span>
        </div>

        <div className="flex items-start gap-3">
          <span className="text-2xl font-bold">4.</span>
          <span className="text-xl">
            You can use your HSA or FSA to pay for Willow prescriptions at
            checkout.
          </span>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
          <FormLoader isLoading={isFormPending || isLoading}>
            <Button
              size={'lg'}
              variant={'electric'}
              className="flex w-full max-w-none justify-between"
              type={'submit'}
              disabled={isFormPending}
            >
              <span>CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>
    </OnboardingTitle>
  );
};

export default SSNSuccess;
