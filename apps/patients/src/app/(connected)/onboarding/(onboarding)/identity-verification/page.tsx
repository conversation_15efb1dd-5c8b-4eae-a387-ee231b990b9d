'use client';

import { useState } from 'react';
import Image from 'next/image';
import arrow from '@/assets/svg/arrow-circle.svg';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import { InfoDialog } from '@/components/ui/infoDialog';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useOnboardingNext } from '@/hooks/onboarding';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { useForm } from 'react-hook-form';

const IdentityVerification = () => {
  const analyticsData = useAnalyticsData();
  const analytics = useAnalytics();
  const form = useForm();
  const { mutateAsync: next, isPending: isFormPending } = useOnboardingNext();
  const [isLoading, setIsLoading] = useState(false);
  const [showInfoDialog, setShowInfoDialog] = useState(false);

  const { onboarding, nextOnboardingStep } = useOnboardingNavigation();

  const onSubmit = async () => {
    try {
      setIsLoading(true);
      void analytics?.track('Identity Verification Started', analyticsData);
      const response = await next({});
      nextOnboardingStep(response.data);
    } catch (e: any) {
      setIsLoading(false);
      form.setError('unsure', { message: e.response?.data?.message });
    }
  };

  if (!onboarding) return null;

  return (
    <OnboardingTitle
      title="Help us verify your identity so a doctor can legally prescribe you medication"
      subtitle="To continue with treatment, we need to very your identity."
    >
      <button
        className="cursor-pointer text-center text-xl text-white underline hover:opacity-80"
        onClick={() => setShowInfoDialog(true)}
        type="button"
      >
        Why do you need to identify my identity?
      </button>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
          <FormLoader isLoading={isFormPending || isLoading}>
            <Button
              size={'lg'}
              variant={'electric'}
              className="flex w-full max-w-none justify-between"
              type={'submit'}
              disabled={isFormPending}
            >
              <span>CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </Button>
          </FormLoader>
        </form>
      </Form>

      {showInfoDialog && (
        <InfoDialog
          title="Why do you need to identify my identity?"
          confirmBtnText={
            <div className="flex w-full items-center justify-between">
              <span>ACCEPT AND CONTINUE</span>
              <Image alt="arrow" src={arrow} style={{ objectFit: 'contain' }} />
            </div>
          }
          confirmBtnClick={() => setShowInfoDialog(false)}
          variant="dark"
        >
          <div className="text-center text-xl font-normal text-white">
            A medical provider is required to verify your identity before
            prescribing medication. Your personal identifiable information,
            medical history, and ID will remain confidential within our secure
            platform.
          </div>
        </InfoDialog>
      )}
    </OnboardingTitle>
  );
};

export default IdentityVerification;
