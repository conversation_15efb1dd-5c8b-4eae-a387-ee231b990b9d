'use client';

import { useEffect, useRef, useState } from 'react';
import OnboardingTitle from '@/components/onboarding/OnboardingTitle';
import { Button } from '@/components/ui/button';
import { Form, FormLoader } from '@/components/ui/form';
import { InfoDialog } from '@/components/ui/infoDialog';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useSkipSSN, useVerifySSN } from '@/hooks/onboarding';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { useOnboardingNavigation } from '@/hooks/useOnboardingNavigation';
import { cn } from '@/lib/utils';
import { useForm } from 'react-hook-form';

interface FormData {
  digit1: string;
  digit2: string;
  digit3: string;
  digit4: string;
}

const SSNCheck = () => {
  const analyticsData = useAnalyticsData();
  const analytics = useAnalytics();
  const form = useForm<FormData>();
  const { mutateAsync: verifySSN, isPending: isVerifying } = useVerifySSN();
  const { mutateAsync: skipSSN, isPending: isSkipping } = useSkipSSN();
  const { onboarding, nextOnboardingStep } = useOnboardingNavigation();

  const inputRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];

  const [digits, setDigits] = useState(['', '', '', '']);
  const [showInfoDialog, setShowInfoDialog] = useState(false);

  // Initialize form with existing data from context
  useEffect(() => {
    if (onboarding?.context) {
      const lastFourSSN = onboarding.context.lastFourSSN;

      if (lastFourSSN) {
        // Populate form with existing SSN
        const existingDigits = lastFourSSN.split('');
        setDigits(existingDigits);

        // Set form values
        existingDigits.forEach((digit, index) => {
          form.setValue(`digit${index + 1}` as keyof FormData, digit);
        });

        // Focus on last input if SSN exists, first input otherwise
        const focusIndex = lastFourSSN ? 3 : 0;
        setTimeout(() => {
          inputRefs[focusIndex]?.current?.focus();
        }, 0);
      } else {
        // No existing SSN, focus on first input
        setTimeout(() => {
          inputRefs[0]?.current?.focus();
        }, 0);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onboarding, form]);

  const handleDigitChange = (index: number, value: string) => {
    if (!/^\d?$/.test(value)) return;

    const newDigits = [...digits];
    newDigits[index] = value;
    setDigits(newDigits);

    form.setValue(`digit${index + 1}` as keyof FormData, value);

    if (value && index < 3) {
      inputRefs[index + 1]?.current?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !digits[index] && index > 0) {
      inputRefs[index - 1]?.current?.focus();
    }
  };

  const onSubmit = async () => {
    try {
      const lastFourSSN = digits.join('');

      if (lastFourSSN.length !== 4) {
        form.setError('digit1', { message: 'Please enter all 4 digits' });
        return;
      }

      void analytics?.track('SSN Verification Attempted', analyticsData);

      const response = await verifySSN({ lastFourSSN });
      nextOnboardingStep(response.data);
    } catch (e: any) {
      form.setError('digit1', {
        message: e.response?.data?.message || 'Verification failed',
      });
    }
  };

  const onSkip = async () => {
    try {
      void analytics?.track('SSN Verification Skipped', analyticsData);

      const response = await skipSSN();
      nextOnboardingStep(response.data);
    } catch (e: any) {
      form.setError('digit1', {
        message: e.response?.data?.message || 'Skip failed',
      });
    }
  };

  if (!onboarding) return null;

  const isLoading = isVerifying || isSkipping;
  // Disable continue if: form incomplete or loading
  const isContinueDisabled = digits.some((digit) => !digit) || isLoading;

  return (
    <OnboardingTitle
      title="Please provide the last 4 digits of your social security number"
      subtitle="To continue with treatment, we need to very your identity."
    >
      <button
        className="cursor-pointer text-center text-xl text-white underline hover:opacity-80"
        onClick={() => setShowInfoDialog(true)}
        type="button"
      >
        Why do we need this?
      </button>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full space-y-8"
        >
          <div className="flex justify-center gap-4">
            {inputRefs.map((ref, index) => (
              <input
                key={index}
                ref={ref}
                type="tel"
                inputMode="numeric"
                pattern="[0-9]"
                maxLength={1}
                value={digits[index]}
                onChange={(e) => handleDigitChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                className={cn(
                  'h-16 w-16 rounded-lg border-2 bg-transparent text-center text-2xl text-white focus:border-electric focus:outline-none',
                  digits[index] ? 'border-electric' : 'border-white',
                )}
              />
            ))}
          </div>

          <div className="text-center text-white">
            <span>Don't want to provide this?</span>
            <br />
            <button
              type="button"
              onClick={onSkip}
              className="text-white underline hover:text-yellow-400"
              disabled={isLoading}
            >
              upload your ID and a photo
            </button>
            <span> instead.</span>
          </div>

          <div className="text-center text-sm text-white">
            256-BIT TLS SECURITY
          </div>

          <FormLoader isLoading={isLoading}>
            <Button
              size={'lg'}
              variant={'electric'}
              className="w-full max-w-none"
              type={'submit'}
              disabled={isContinueDisabled}
              style={{
                backgroundColor: isContinueDisabled ? '#6b7280' : undefined,
                cursor: isContinueDisabled ? 'not-allowed' : 'pointer',
              }}
            >
              CONTINUE
            </Button>
          </FormLoader>
        </form>
      </Form>

      {showInfoDialog && (
        <InfoDialog
          title="Why do we need the last 4 digits of your SSN?"
          confirmBtnText="I UNDERSTAND"
          confirmBtnClick={() => setShowInfoDialog(false)}
          variant="dark"
        >
          <div className="text-center text-xl font-normal text-white">
            The last 4 digits of your social security number help us confirm
            your identity. Your personal identifiable information, medical
            history, and ID will remain confidential within our secure platform.
            This is an optional step. If you prefer, you can verify your
            identity by uploading a photo and your ID.
          </div>
        </InfoDialog>
      )}
    </OnboardingTitle>
  );
};

export default SSNCheck;
