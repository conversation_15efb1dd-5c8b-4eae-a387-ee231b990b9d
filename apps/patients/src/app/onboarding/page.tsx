'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { onboardingData<PERSON>tom, onboardingVersionAtom } from '@/store/store';
import { useMutation } from '@tanstack/react-query';
import { useSetAtom } from 'jotai';

import { apiClient } from '@willow/utils/api/client';

interface InitializeResponse {
  version: 'v1' | 'legacy-v1' | 'legacy-v2';
  initialized: boolean;
  currentState?: string;
  canTransition?: boolean;
  isComplete?: boolean;
  context?: any;
  events?: string[];
  stepName?: string;
  percentage?: number;
}

const OnboardingInitialize = () => {
  const router = useRouter();
  const setOnboardingData = useSetAtom(onboardingDataAtom);
  const setOnboardingVersion = useSetAtom(onboardingVersionAtom);

  const { mutate: initializeOnboarding } = useMutation<InitializeResponse>({
    mutationFn: () =>
      apiClient.post('/onboarding/initialize').then((res) => res.data),
    onSuccess: (data) => {
      // Store the version for later use
      setOnboardingVersion(data.version);

      if (data.version.startsWith('legacy')) {
        // Handle legacy flow
        router.push('/account/state');
      } else {
        handleNavigation(data);
      }
    },
    onError: () => {
      // Fallback to legacy flow on error
      router.push('/account/state');
    },
  });

  const handleNavigation = (data: InitializeResponse) => {
    // Set initial onboarding data
    setOnboardingData({
      state: data.currentState || 'preSignup.stateSelection',
      context: data.context || {
        productType: '',
        questionnaireCompleted: false,
        questionnaire: {},
      },
      events: data.events || [],
      stepName: data.stepName || 'Account Creation',
      percentage: data.percentage || 0,
    });

    const currentState = data.currentState || 'preSignup.stateSelection';

    // Route based on current state
    if (currentState.startsWith('preSignup.')) {
      const stateMap: Record<string, string> = {
        'preSignup.stateSelection': '/account/state',
        'preSignup.firstAndLastName': '/account/name',
        'preSignup.createAccount': '/account/signup',
        'preSignup.unsupportedState': '/account/unsupported',
        'preSignup.unsupportedStateThankYou': '/account/thank-you',
      };

      const route = stateMap[currentState] || '/account/state';
      router.push(route);
    } else if (currentState.startsWith('questionnaire.')) {
      // User has completed pre-signup, route to questionnaire
      router.push(`/onboarding/questionnaire/${currentState.split('.')[1]}`);
    } else {
      // Route to appropriate post-questionnaire state
      router.push(`/onboarding/${currentState}`);
    }
  };

  useEffect(() => {
    initializeOnboarding();
  }, []);

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-semibold">Initializing your journey...</h2>
        <div className="mt-4">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-gray-900"></div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingInitialize;
