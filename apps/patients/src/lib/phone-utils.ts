/**
 * Formats a phone number string to (XXX) XXX-XXXX format
 * @param phone - Phone number string (can contain digits, spaces, dashes, etc.)
 * @returns Formatted phone number string or original if invalid
 */
export function formatPhoneNumber(phone: string): string {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');

  // Check if we have exactly 10 digits
  if (digits.length !== 10) {
    return phone; // Return original if not a valid 10-digit number
  }

  // Format as (XXX) XXX-XXXX
  const areaCode = digits.slice(0, 3);
  const prefix = digits.slice(3, 6);
  const lineNumber = digits.slice(6, 10);

  return `(${areaCode}) ${prefix}-${lineNumber}`;
}

/**
 * Removes formatting from a phone number, leaving only digits
 * @param phone - Formatted phone number string
 * @returns Phone number with only digits
 */
export function unformatPhoneNumber(phone: string): string {
  return phone.replace(/\D/g, '');
}
