version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - npm install -g pnpm
            - pnpm install
            - pnpm -F @willow/db generate
        build:
          commands:
            - pnpm turbo build --filter=@willow/doctors
      artifacts:
        baseDirectory: apps/doctors/.next
        files:
          - '**/*'
      cache:
        paths:
          - .next/cache/**/*
          - node_modules/**/*
      buildPath: /
    appRoot: apps/doctors
