'use client';

import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

import { useCurrentPatient } from '~/hooks/patient';
import { DoctorAdminConversation } from './Chat';
import { CreateConversation } from './CreateConversation';

export function ConversationAdmin() {
  const patient = useCurrentPatient();
  const queryClient = useQueryClient();

  function invalidatePatientInfo() {
    if (patient?.id) {
      queryClient.invalidateQueries({
        queryKey: ['patient', patient.id, 'info'],
      });
    }
  }
  useEffect(() => {
    invalidatePatientInfo();
  }, []);

  if (!patient) return null;

  return (
    <>
      {patient.doctorAdminConversation?.id ? (
        <DoctorAdminConversation
          conversationId={patient.doctorAdminConversation?.id}
        />
      ) : (
        <CreateConversation patientId={patient.id} />
      )}
    </>
  );
}
