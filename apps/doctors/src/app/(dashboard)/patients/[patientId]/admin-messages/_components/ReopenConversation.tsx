import { Button } from '@willow/ui/base/button';

import { useReopenDoctorAdminConversation } from '~/hooks/chat';

export function ReopenConversation({
  conversationId,
  patientId,
}: {
  conversationId: string;
  patientId: string;
}) {
  const { mutate: reopenDoctorAdminConversation, isPending } =
    useReopenDoctorAdminConversation();

  return (
    <Button
      size="sm"
      variant="denim"
      onClick={() => {
        reopenDoctorAdminConversation({
          conversationId,
          patientId,
        });
      }}
      className="w-1/2"
      loading={isPending}
    >
      Reopen Discussion
    </Button>
  );
}
