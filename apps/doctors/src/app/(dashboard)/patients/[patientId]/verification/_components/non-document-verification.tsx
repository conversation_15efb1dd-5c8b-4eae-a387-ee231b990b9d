'use client';

import { useCallback, useMemo, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import closeBtn from '@/assets/svg/close-btn.svg';
import { RotateCw, Upload } from 'lucide-react';

import { Button } from '@willow/ui/base/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@willow/ui/base/dialog';
import { useToast } from '@willow/ui/base/use-toast';
import { createImage } from '@willow/ui/image-upload-dialog';

import { env } from '~/env';
import { useCurrentPatient, useUpdatePatientPhoto } from '~/hooks/patient';
import IdentityIssueDialog from './identity-issue-dialog';
import { NonDocumentPatientInfo } from './non-document-patient-info';

interface NonDocumentVerificationProps {
  isSubmitting: boolean;
  onVerify: () => void;
}

export function NonDocumentVerification({
  isSubmitting,
  onVerify,
}: NonDocumentVerificationProps) {
  const patient = useCurrentPatient();

  if (!patient) {
    return <div className="p-14">Loading patient data...</div>;
  }

  return (
    <div className="min-h-fullh flex flex-col p-14">
      <div className="flex justify-between">
        <div className="flex flex-col text-denim">
          <div className="text-xl">Patient identity matched</div>
          <div className="text-sm">Non-document Verification</div>
        </div>
        <Link href="/">
          <div className="flex items-center">
            <div>
              <Image src={closeBtn} width={30} height={30} alt="overview" />
            </div>
          </div>
        </Link>
      </div>

      <div className="mt-10">
        <div className="grid grid-cols-1 items-start gap-6 xl:grid-cols-2">
          <NonDocumentPatientInfo />
          <FacePhotoDisplay
            patientId={patient.id}
            facePhoto={patient.facePhoto}
            isSubmitting={isSubmitting}
          />
        </div>
      </div>

      <div className="my-15 mb-5 mt-20 flex gap-3 self-end">
        <IdentityIssueDialog
          disabled={isSubmitting}
          patient={patient}
          isSubmitting={isSubmitting}
        />
        <Button
          onClick={onVerify}
          disabled={isSubmitting}
          size={'sm'}
          variant="denim"
          className="font-light text-white"
        >
          VERIFY
        </Button>
      </div>
    </div>
  );
}

interface FacePhotoDisplayProps {
  patientId: string;
  facePhoto: string;
  isSubmitting: boolean;
}

function FacePhotoDisplay({
  patientId,
  facePhoto,
  isSubmitting,
}: FacePhotoDisplayProps) {
  const { toast } = useToast();
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [isRotating, setIsRotating] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [timestamp, setTimestamp] = useState<number>(new Date().getTime());

  const originalImageUrl = useMemo(() => {
    return `${env.NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL}/${facePhoto}?cache=${timestamp}`;
  }, [facePhoto, timestamp]);

  const imageUrl = useMemo(() => {
    return originalImageUrl;
  }, [originalImageUrl]);

  // Create a proxied URL for fetching to avoid CORS issues
  const proxiedImageUrl = useMemo(() => {
    return `/api/proxy-image?url=${encodeURIComponent(originalImageUrl)}`;
  }, [originalImageUrl]);

  const { mutateAsync: upload, isPending } = useUpdatePatientPhoto(
    patientId,
    'face-photo',
  );

  const onUpload = useCallback(
    (image: Blob) => {
      setIsUploading(true);
      return upload(image, {
        onSuccess: () => {
          // Force refresh the image by updating timestamp with a new value
          setTimestamp(new Date().getTime());
          setIsUploading(false);
        },
        onError: (error) => {
          toast({
            variant: 'destructive',
            title: 'Error uploading photo',
            description: (error as Error).message,
          });
          setIsUploading(false);
        },
      }) as Promise<void>;
    },
    [upload, toast, setTimestamp, setIsUploading],
  );

  const onRotateImage = useCallback(async () => {
    try {
      setIsRotating(true);
      // Fetch the current image through our proxy to avoid CORS issues
      const response = await fetch(proxiedImageUrl);
      const imageBlob = await response.blob();
      const imageDataUrl = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.readAsDataURL(imageBlob);
      });

      // Create an image element to get dimensions
      const img = await createImage(imageDataUrl);

      // Rotate the image 90 degrees clockwise while preserving aspect ratio
      // For rotation, we need to swap width and height to maintain aspect ratio
      const rotatedBlob = await new Promise<Blob>((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          throw new Error('Failed to get canvas context');
        }

        // Swap width and height for 90 degree rotation
        canvas.width = img.height;
        canvas.height = img.width;

        // Translate and rotate around the center
        ctx.translate(canvas.width / 2, canvas.height / 2);
        ctx.rotate(Math.PI / 2); // 90 degrees in radians
        ctx.drawImage(img, -img.width / 2, -img.height / 2);

        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            throw new Error('Canvas is empty');
          }
        }, 'image/jpeg');
      });

      // Upload the rotated image
      await onUpload(rotatedBlob);

      // Force refresh the image by updating timestamp
      setTimestamp(new Date().getTime());

      toast({
        title: 'Image Rotated',
        description: 'The image has been rotated successfully',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error Rotating Photo',
        description: (error as Error).message,
      });
    } finally {
      setIsRotating(false);
    }
  }, [proxiedImageUrl, onUpload, toast, setTimestamp]);

  return (
    <div>
      <div className="mb-4 text-base font-normal text-zinc-900">Photo</div>
      <div className="rounded-lg bg-white pb-6">
        <div
          className="border-store my-3 flex h-[350px] cursor-pointer items-center justify-center overflow-hidden rounded-lg border p-4"
          onClick={() => setIsPreviewModalOpen(true)}
        >
          <div className="relative h-full w-full">
            <Image
              src={imageUrl}
              alt="patient face photo"
              layout="fill"
              objectFit="contain"
            />
          </div>
        </div>
        <div className="flex justify-end gap-2">
          {!isSubmitting && (
            <>
              <Button
                size={'xs'}
                variant="denimOutline"
                className="py-4 [&_svg]:size-4"
                onClick={onRotateImage}
                loading={isRotating}
                disabled={isRotating || isPending || isUploading}
              >
                <RotateCw /> Rotate Image
              </Button>
              <Dialog
                open={isUploadDialogOpen}
                onOpenChange={setIsUploadDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button
                    size={'xs'}
                    variant="denimOutline"
                    className="py-4 [&_svg]:size-4"
                    disabled={isRotating || isPending || isUploading}
                  >
                    <Upload /> Upload Image
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Upload and Edit Image</DialogTitle>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div
                      onDrop={(e) => {
                        e.preventDefault();
                        const file = e.dataTransfer.files[0];
                        if (file) {
                          const reader = new FileReader();
                          reader.onload = async () => {
                            try {
                              setIsUploading(true);
                              const blob = await fetch(
                                reader.result as string,
                              ).then((r) => r.blob());
                              await onUpload(blob);
                              setIsUploadDialogOpen(false);
                            } catch (error) {
                              toast({
                                variant: 'destructive',
                                title: 'Error uploading photo',
                                description: (error as Error).message,
                              });
                            } finally {
                              setIsUploading(false);
                            }
                          };
                          reader.readAsDataURL(file);
                        }
                      }}
                      onDragOver={(e) => e.preventDefault()}
                      className="cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-12 text-center"
                    >
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            const reader = new FileReader();
                            reader.onload = async () => {
                              try {
                                setIsUploading(true);
                                const blob = await fetch(
                                  reader.result as string,
                                ).then((r) => r.blob());
                                await onUpload(blob);
                                setIsUploadDialogOpen(false);
                              } catch (error) {
                                toast({
                                  variant: 'destructive',
                                  title: 'Error uploading photo',
                                  description: (error as Error).message,
                                });
                              } finally {
                                setIsUploading(false);
                              }
                            };
                            reader.readAsDataURL(file);
                          }
                        }}
                        className="hidden"
                        id="file-upload-face-photo"
                      />
                      <label
                        htmlFor="file-upload-face-photo"
                        className="cursor-pointer"
                      >
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="mt-2 text-sm text-gray-500">
                          {isUploading
                            ? 'Uploading...'
                            : 'Drag and drop or click to upload'}
                        </p>
                      </label>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="denimOutline"
                      size="sm"
                      onClick={() => setIsUploadDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </>
          )}
        </div>

        <Dialog open={isPreviewModalOpen} onOpenChange={setIsPreviewModalOpen}>
          <DialogContent className="flex h-[95vh] max-h-[95vh] w-[95vw] max-w-[95vw] flex-col p-0">
            <DialogHeader className="border-b bg-white p-4">
              <DialogTitle>Photo</DialogTitle>
            </DialogHeader>
            <div className="relative w-full flex-1 overflow-hidden">
              <Image
                src={imageUrl}
                alt="patient face photo"
                layout="fill"
                objectFit="contain"
              />
            </div>
            <DialogFooter className="bg-white p-1"></DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
