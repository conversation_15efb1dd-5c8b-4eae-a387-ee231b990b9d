import { useCallback, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@willow/ui/base/button';
import { Checkbox } from '@willow/ui/base/checkbox';
import { Dialog, DialogContent } from '@willow/ui/base/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@willow/ui/base/form';
import {
  MEDICAL_NECESSITY_MAPPINGS,
  SYSTEM_ONLY_NECESSITIES,
} from '@willow/utils';

import type { Patient } from '~/data/patient-types';
import {
  useCurrentPatient,
  useUpdateMedicalNecessities,
} from '~/hooks/patient';

const updateMedicalNecessitiesSchema = z.object({
  necessities: z.array(z.string()),
});

type UpdateMedicalNecessitiesSchema = z.infer<
  typeof updateMedicalNecessitiesSchema
>;

export const EditMedicalNecessityDialog = ({
  open,
  setOpen,
  patientId,
}: {
  open: boolean;
  patientId: string;
  setOpen: (value: boolean) => void;
}) => {
  const patient = useCurrentPatient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  if (!patient) return null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="p-8 sm:max-w-[690px]">
        <div className="grid grid-cols-4 gap-8">
          <div className="text-2xl font-medium text-denim">
            <h3>Medical Necessity</h3>
          </div>
          <div className="col-span-3">
            <EditMedicalNecessityForm
              patientId={patientId}
              handleClose={handleClose}
              patient={patient}
              isSubmitting={isSubmitting}
              setIsSubmitting={setIsSubmitting}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const EditMedicalNecessityForm = ({
  patientId,
  handleClose,
  patient,
  isSubmitting,
  setIsSubmitting,
}: {
  patientId: string;
  handleClose: () => void;
  patient: Patient;
  isSubmitting: boolean;
  setIsSubmitting: (value: boolean) => void;
}) => {
  const { mutateAsync: updateMedicalNecessities } =
    useUpdateMedicalNecessities(patientId);

  // Get currently selected necessities and their setBy info
  const currentNecessities =
    patient.medicalNecessities.map((mn) => mn.necessity) || [];

  // Create a map of necessity to setBy for easy lookup
  const necessitySetByMap =
    patient.medicalNecessities.reduce((acc: Record<string, string>, mn) => {
      acc[mn.necessity] = mn.setBy;
      return acc;
    }, {}) || {};

  // Only include doctor-set necessities in the form (editable ones)
  const editableNecessities = currentNecessities.filter(
    (necessity: string) => necessitySetByMap[necessity] === 'doctor',
  );

  const form = useForm<UpdateMedicalNecessitiesSchema>({
    resolver: zodResolver(updateMedicalNecessitiesSchema),
    defaultValues: {
      necessities: editableNecessities,
    },
  });

  const onSubmitHandler = async (data: UpdateMedicalNecessitiesSchema) => {
    setIsSubmitting(true);
    try {
      await updateMedicalNecessities(data.necessities);
      handleClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get the latest updated date
  const lastUpdated =
    patient.medicalNecessities.length > 0
      ? new Date(
          Math.max(
            ...patient.medicalNecessities.map((mn: any) =>
              new Date(mn.updatedAt).getTime(),
            ),
          ),
        )
      : null;

  const medicalNecessityOptions = Object.entries(MEDICAL_NECESSITY_MAPPINGS)
    .sort(([, a], [, b]) => a.priority - b.priority)
    .map(([key, value]) => [key, value.text] as const);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
        <p className="text-denim">
          <span>for</span>
          {'  '}
          <span className="text-base font-semibold">
            {patient.user.firstName} {patient.user.lastName}
          </span>
        </p>

        {lastUpdated && (
          <p className="text-sm text-stone">
            Last updated: {lastUpdated.toLocaleDateString()}
          </p>
        )}

        <FormField
          control={form.control}
          name="necessities"
          render={() => (
            <FormItem>
              <FormLabel className="text-sm font-normal text-denim">
                Medical Necessity Options
              </FormLabel>
              <div className="space-y-3">
                {medicalNecessityOptions.map(([key, label]) => {
                  const setBy = necessitySetByMap[key];
                  const isSelected = currentNecessities.includes(key);
                  const isReadOnly = setBy === 'patient';
                  const isSystemOnly = SYSTEM_ONLY_NECESSITIES.includes(
                    key as any,
                  );

                  // Hide system-only necessities completely
                  if (isSystemOnly) return null;

                  return (
                    <FormField
                      key={key}
                      control={form.control}
                      name="necessities"
                      render={({ field }) => {
                        const checkboxId = `medical-necessity-${key}`;

                        return (
                          <FormItem
                            key={key}
                            className={`flex flex-row items-start space-x-3 space-y-0 ${
                              isReadOnly ? 'opacity-60' : ''
                            }`}
                          >
                            <FormControl>
                              <Checkbox
                                id={checkboxId}
                                checked={
                                  isReadOnly
                                    ? isSelected
                                    : field.value?.includes(key)
                                }
                                disabled={isReadOnly}
                                onCheckedChange={(checked) => {
                                  if (isReadOnly) return;
                                  return checked
                                    ? field.onChange([...field.value, key])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) => value !== key,
                                        ),
                                      );
                                }}
                              />
                            </FormControl>
                            <label
                              htmlFor={checkboxId}
                              className={`text-sm font-normal ${
                                isReadOnly
                                  ? 'cursor-not-allowed'
                                  : 'cursor-pointer'
                              }`}
                            >
                              {label}
                              {isReadOnly && (
                                <span className="ml-2 text-xs text-stone">
                                  (Set by patient)
                                </span>
                              )}
                            </label>
                          </FormItem>
                        );
                      }}
                    />
                  );
                })}
              </div>
            </FormItem>
          )}
        />

        <div className="flex w-full gap-4">
          <Button
            variant={'denimOutline'}
            className="flex-1 border text-sm font-light uppercase"
            size="sm"
            type="reset"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            variant={'denim'}
            className="flex-1 text-sm font-light uppercase text-white"
            size="sm"
            type="submit"
            loading={isSubmitting}
            disabled={isSubmitting}
          >
            Save Changes
          </Button>
        </div>
      </form>
    </Form>
  );
};
