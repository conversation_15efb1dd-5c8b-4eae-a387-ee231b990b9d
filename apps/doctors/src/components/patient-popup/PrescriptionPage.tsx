'use client';

import type { Patient, Pharmacy, ProductWithVials } from '@/data/patient-types';
import { useMemo, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import SelectPharmacy from '@/components/patient-popup/SelectPharmacy';
import SubscriptionStartDate from '@/components/patient-popup/SubscriptionStartDate';
import { useCreateTreatment } from '@/hooks/treatments';
import { zodResolver } from '@hookform/resolvers/zod';
import { addHours, format } from 'date-fns';
import { Plus } from 'lucide-react';
import { useFieldArray, useForm } from 'react-hook-form';
import { z } from 'zod';

import type { PrescribePatientPayload, PrescriptionItem } from '@willow/db';
import { Button } from '@willow/ui/base/button';

import { env } from '~/env';
import { useGetPatientScheduledFollowUp } from '~/hooks/patient';
import { useLog } from '~/hooks/useLog';
import { combineDateTime } from '~/lib/utils';
import { Form } from '../ui/form';
import ActivePrescriptionList from './ActivePrescriptionList';
import SwitchFormField from './SwitchFormField';

interface PrescriptionPageProps {
  data: {
    desiredTreatmentsProducts: ProductWithVials[];
    productsByPharmacy: Record<string, ProductWithVials>;
    pharmacies: Pharmacy[];
  };
  patient: Patient;
}

export const prescriptionSchema = z.object({
  applyWillowDiscount: z.boolean(),
  prescriptions: z.array(
    z.object({
      prescription: z.string().min(1, { message: 'Prescription is required' }),
      productPriceId: z.string().min(1, { message: 'Dosage is required' }),
      finalProductPriceId: z.string().nullable().optional(),
      refills: z
        .number({ invalid_type_error: 'Refills is required' })
        .gte(0, 'Refills is required')
        .or(z.string().min(1, { message: 'Refills is required' })),
      vials: z.string().min(1, { message: 'Vials is required' }),
      refillSystem: z.string().min(1, { message: 'System is required' }),
      shortInitialPrescription: z.boolean().default(false),
      notes: z.string(),
    }),
  ),
});

const PrescriptionPage = ({ data, patient }: PrescriptionPageProps) => {
  const { initTracedLog } = useLog();
  const router = useRouter();
  const { data: patientFollowUp } = useGetPatientScheduledFollowUp(patient.id);
  const baseUrl = env.NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL;
  const timestamp = new Date().getTime();

  // State
  const [startDate, setStartDate] = useState<Date>(addHours(new Date(), 36));
  const [selectedPharmacyId, setSelectedPharmacyId] = useState<string>(
    patient.pharmacy.id || '',
  );

  // Mutations
  const { mutateAsync: prescribePatient, isPending } = useCreateTreatment();

  // Memoize products based on selected pharmacy
  const products = useMemo(() => {
    return data.productsByPharmacy[selectedPharmacyId] || [];
  }, [selectedPharmacyId, data.productsByPharmacy]);

  // Memoize desired treatments sorted by type
  const desiredTreatmentsProducts = useMemo(() => {
    if (patient.hasTreatment) return [];

    return [...data.desiredTreatmentsProducts].sort((a, b) => {
      if (a.metadata.type === 'core' && b.metadata.type === 'additional')
        return -1;
      if (a.metadata.type === 'additional' && b.metadata.type === 'core')
        return 1;
      return 0;
    });
  }, [patient.hasTreatment, data.desiredTreatmentsProducts]);

  // Derive initial form values from desired treatments
  const initialPrescriptions = useMemo(() => {
    if (patient.hasTreatment || desiredTreatmentsProducts.length === 0) {
      return [
        {
          prescription: '',
          productPriceId: '',
          refills: '0',
          refillSystem: '',
          notes: '',
          finalProductPriceId: '',
          vials: '1',
          shortInitialPrescription: false,
        },
      ];
    }

    console.log('desiredTreatmentsProducts', desiredTreatmentsProducts);
    return desiredTreatmentsProducts.map((product, index) => {
      const isSemaglutideInjectable =
        product.label.toLowerCase() == 'semaglutide' &&
        product.metadata.form === 'injectable';
      const isTirzepatideInjectable =
        product.label.toLowerCase() == 'tirzepatide' &&
        product.metadata.form === 'injectable';

      const tirzepatideFinalProductPriceId = isTirzepatideInjectable
        ? product.productPrice.find((p) => p.milligrams == 6.375)?.id || ''
        : '';
      console.log(
        'PP',
        product.productPrice.find((pp) => pp.milligrams == 6.375),
      );
      return {
        prescription: product.id,
        productPriceId: product.defaultPriceId,
        refills: isSemaglutideInjectable
          ? '14'
          : isTirzepatideInjectable
            ? '3'
            : '0',
        refillSystem:
          product.metadata.type === 'additional' ? 'static' : 'scaling',
        notes: '',
        finalProductPriceId: isSemaglutideInjectable
          ? 'none'
          : tirzepatideFinalProductPriceId,
        vials: product.vials.toString(),
        shortInitialPrescription:
          product.metadata.type !== 'additional' && index == 0,
      };
    });
  }, [desiredTreatmentsProducts, patient.hasTreatment]);

  // Setup form with initial values
  const prescriptionForm = useForm<z.infer<typeof prescriptionSchema>>({
    resolver: zodResolver(prescriptionSchema),
    defaultValues: {
      prescriptions: initialPrescriptions,
      applyWillowDiscount: false,
    },
  });

  // Field array for prescriptions
  const prescriptionFormPart = useFieldArray({
    name: 'prescriptions',
    control: prescriptionForm.control,
  });

  // Handle pharmacy change
  const handlePharmacyChange = (newPharmacyId: string) => {
    setSelectedPharmacyId(newPharmacyId);

    // Reset form when pharmacy changes, but maintain 21-day checkbox for new patients with core prescriptions
    prescriptionForm.reset({
      prescriptions: [
        {
          prescription: '',
          productPriceId: '',
          refills: '0',
          refillSystem: '',
          notes: '',
          finalProductPriceId: '',
          // For new patients, we want to keep the 21-day checkbox enabled by default
          shortInitialPrescription: !patient.hasTreatment,
          vials: '1',
        },
      ],
      applyWillowDiscount: false,
    });
  };

  // Add new prescription
  const addPrescription = () => {
    // Check if this is the first prescription being added
    const isFirstPrescription = prescriptionFormPart.fields.length === 0;

    prescriptionFormPart.append({
      prescription: '',
      productPriceId: '',
      refills: '0',
      refillSystem: '',
      notes: '',
      // Only set shortInitialPrescription to true for first prescription of new patients
      shortInitialPrescription: !patient.hasTreatment && isFirstPrescription,
      vials: '1',
    });
  };

  // Watch prescriptions to check if all vials are one
  const prescriptions = prescriptionForm.watch('prescriptions');
  const allVialsAreOne = prescriptions?.every((p) => p.vials === '1');

  // Disable discount if not all vials are one
  if (!allVialsAreOne && prescriptionForm.getValues('applyWillowDiscount')) {
    prescriptionForm.setValue('applyWillowDiscount', false);
  }

  // Handle prescription submission
  const handlePrescription = async (
    formValue: z.infer<typeof prescriptionSchema>,
  ) => {
    const log = initTracedLog();
    const startTime = format(addHours(new Date(), 36), 'HH:mm');
    const startDateWithTime = combineDateTime(startDate, startTime);

    try {
      log({
        aggregateId: 'doctor-prescribe-patient',
        event: 'on-sumbit',
        currentPatient: {
          id: patient.id,
          userId: patient.id,
          email: patient.id,
        },
        data: {
          formValue,
          startDate: startDateWithTime,
        },
      });

      const prescriptionData: PrescriptionItem[] = formValue.prescriptions.map(
        (item) => ({
          refills: Number(item.refills),
          refillSystem: item.refillSystem,
          initialProductPriceId: item.productPriceId ?? '',
          finalProductPriceId:
            item.finalProductPriceId === 'none'
              ? null
              : item.finalProductPriceId || null,
          shortInitialPrescription: item.shortInitialPrescription,
          vials: item.vials,
          notes: item.notes,
          delayUntil: startDateWithTime.toISOString(),
        }),
      );

      const payload: PrescribePatientPayload = {
        patientId: patient.id,
        pharmacyId: selectedPharmacyId,
        applyDiscount: formValue.applyWillowDiscount,
        prescription: prescriptionData,
      };

      log({
        aggregateId: 'doctor-prescribe-patient',
        event: 'prescribe',
        data: payload,
      });

      await prescribePatient(payload);

      log({
        aggregateId: 'doctor-prescribe-patient',
        event: 'prescribe-success',
        data: payload,
      });

      router.push(`/patients/${patient.id}/treatment`);
    } catch (error) {
      log({
        aggregateId: 'doctor-prescribe-patient',
        event: 'prescribe-error',
        data: error,
      });
    }
  };

  if (!patient || !desiredTreatmentsProducts || !products || !patientFollowUp)
    return null;

  return (
    <Form {...prescriptionForm}>
      <form
        onSubmit={prescriptionForm.handleSubmit(handlePrescription)}
        className="flex min-h-full flex-col gap-4 p-14"
      >
        <div className="text-sm font-normal text-denim">
          Patient Information
        </div>

        <div className="flex items-start gap-5 bg-gray-200 px-10 py-5">
          <Image
            src={`${baseUrl}/${patient.facePhoto}?${timestamp}`}
            width={100}
            height={100}
            className="rounded-full"
            alt="patient avatar"
          />

          <div className="flex flex-1 pt-4">
            <div>
              <div className="text-base font-normal text-neutral-600">
                {patient.user.firstName} {patient.user.lastName}
              </div>
              {!patient.hasTreatment &&
                desiredTreatmentsProducts.length > 0 && (
                  <div className="text-sm font-normal text-neutral-600">
                    Requested Medication:{' '}
                    {desiredTreatmentsProducts
                      .map((p: any) =>
                        p.metadata.type === 'core'
                          ? `${p.metadata.label} (${p.metadata.form}) (${p.vials} Vial${p.vials > 1 ? 's' : ''})`
                          : p.metadata.label,
                      )
                      .join(', ')}
                  </div>
                )}
            </div>
          </div>
        </div>

        <div className="text-sm font-normal text-slate-600">Pharmacy</div>

        <SelectPharmacy
          value={selectedPharmacyId}
          onChangeValue={handlePharmacyChange}
          pharmacies={data.pharmacies}
          currentPharmacyId={patient.pharmacyId}
        />

        <div className="text-sm font-normal text-slate-600">
          Prescription Order
        </div>

        {prescriptionFormPart.fields.length > 0 && (
          <SubscriptionStartDate
            onChangeDate={setStartDate}
            defaultValue={startDate}
          />
        )}

        {prescriptionFormPart.fields.map((field, index) => (
          <ActivePrescriptionList
            key={field.id}
            products={Array.isArray(products) ? products : []}
            onClose={() => prescriptionFormPart.remove(index)}
            form={prescriptionForm}
            index={index}
            product={undefined}
            patientHasTreatment={patient.hasTreatment} // Pass hasTreatment as a prop
          />
        ))}

        <div className="flex">
          <span
            className="flex cursor-pointer items-center gap-1 text-sm font-normal text-denim"
            onClick={addPrescription}
            onKeyDown={undefined}
          >
            <Plus width={14} /> Add new order
          </span>
        </div>

        <div className="mt-auto flex justify-between">
          <div className="rounded-lg bg-gray-50 p-4">
            <SwitchFormField
              form={prescriptionForm}
              name="applyWillowDiscount"
              label="Apply Winona Discount"
              hideErrorMessage
              disabled={!allVialsAreOne}
            />
          </div>

          <div className="flex gap-3">
            <Link href={`/patients/${patient.id}/treatment`}>
              <Button
                variant={'denimOutline'}
                className="flex-1 border px-10 text-sm font-light"
                size="sm"
                type="reset"
              >
                CANCEL
              </Button>
            </Link>
            <Button
              variant={'denim'}
              className="flex-1 px-14 text-sm font-light text-white"
              size="sm"
              type="submit"
              loading={isPending}
              disabled={!prescriptionForm.getValues().prescriptions.length}
            >
              SAVE
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};

export default PrescriptionPage;
