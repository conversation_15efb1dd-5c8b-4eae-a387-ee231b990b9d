import { writeFileSync } from 'fs';
import * as path from 'path';
import type {
  MultiplePrescriptionRequest,
  PharmacyPatient,
  PharmacyPrescriber,
  PharmacyPrescriptionProduct,
  PrescriptionRequest,
} from '@modules/integrations/pharmacy';
import type { TreatmentMachineContext } from '@modules/treatment/states/treatment.state';
import type {
  Doctor,
  Patient,
  PatientShippingAddress,
  PharmacyIntegration,
  Prescription,
  State,
  Treatment,
  User,
} from '@prisma/client';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { PharmacyServiceFactory } from '@modules/integrations/pharmacy';
import { PrescriptionImageGeneratorService } from '@modules/integrations/pharmacy/services/prescription-image-generator.service';
import { Injectable } from '@nestjs/common';
import { Command, CommandRunner, Option } from 'nest-commander';

type PatientWithRelations = Patient & {
  user: User;
  state: State;
  onboardingState: any;
  shippingAddresses: (PatientShippingAddress & {
    state: State;
  })[];
};

type DoctorWithRelations = Doctor & {
  user: User;
  state: State;
  prescribesIn: Array<{
    stateId: string;
    licenseNumber: string | null;
  }>;
};

interface ExtendedPharmacyIntegration extends PharmacyIntegration {
  prescription: Prescription & {
    treatmentId: string;
    productPriceId: string;
    treatment: Treatment & {
      state: any; // Contains TreatmentMachineContext
    };
    patient: PatientWithRelations;
    doctor: DoctorWithRelations;
    productPrice: {
      id: string;
      product: {
        id: string;
        name: string;
        pharmacyId: string;
        pharmacy: {
          id: string;
          slug: string;
        };
      };
    };
  };
}

@Injectable()
@Command({
  name: 'resend-redrock-prescriptions',
  description:
    'Find and resend prescriptions for specific patients sent to Red Rock pharmacy',
})
export class ResendRedRockPrescriptionsCommand extends CommandRunner {
  private readonly RED_ROCK_PHARMACY_ID =
    '23144cec-6b1d-4520-8209-b096da616760';

  constructor(
    private readonly prisma: PrismaService,
    private readonly pharmacyServiceFactory: PharmacyServiceFactory,
    private readonly prescriptionImageGenerator: PrescriptionImageGeneratorService,
  ) {
    super();
  }

  @Option({
    flags: '--dry-run',
    description: 'Show what would be resent without actually resending',
  })
  parseDryRun(): boolean {
    return true;
  }

  @Option({
    flags: '--additional-ids [ids]',
    description:
      'Comma-separated list of additional Red Rock prescription RX IDs to include',
  })
  parseAdditionalIds(val: string): string[] {
    return val.split(/\s*,\s*/).map((id) => id.trim());
  }

  @Option({
    flags: '--limit [number]',
    description:
      'Limit the number of prescriptions to process (useful for testing)',
  })
  parseLimit(val: string): number {
    const limit = parseInt(val, 10);
    if (isNaN(limit) || limit < 1) {
      throw new Error('Limit must be a positive number');
    }
    return limit;
  }

  @Option({
    flags: '--delay [number]',
    description: 'Delay in milliseconds between API requests (default: 1000ms)',
  })
  parseDelay(val: string): number {
    const delay = parseInt(val, 10);
    if (isNaN(delay) || delay < 0) {
      throw new Error('Delay must be a non-negative number');
    }
    return delay;
  }

  @Option({
    flags: '--test [index]',
    description:
      'Test mode: select a specific record by index (0-based), generate prescription image, and output request comparison',
  })
  parseTest(val: string): number {
    const index = parseInt(val, 10);
    if (isNaN(index) || index < 0) {
      throw new Error('Test index must be a non-negative number');
    }
    return index;
  }

  /**
   * Helper function to create a delay
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get patient list with formatted names and DOBs
   */
  private getPatientList(): Array<
    | {
        lastName: string;
        firstName: string;
        dob: string;
      }
    | {
        rxId: string;
      }
  > {
    return [
      { rxId: '1339607' },
      { rxId: '1339794' },
      { rxId: '1339768' },
      { rxId: '1339887' },
      { rxId: '1339751' },
      { rxId: '1339500' },
      { rxId: '1339748' },
      { rxId: '1340612' },
      { rxId: '1340230' },
      { rxId: '1340368' },
      { rxId: '1340501' },
      { rxId: '1341281' },
      { rxId: '1341618' },
      { rxId: '1341431' },
      { rxId: '1341478' },
      { rxId: '1341483' },
      { rxId: '1339735' },
      { rxId: '1339415' },
      { rxId: '1340105' },
      { rxId: '1342267' },
      { rxId: '1342265' },
      { rxId: '1340052' },
      { rxId: '1342269' },
      { rxId: '1342271' },
      { rxId: '1342270' },
      { rxId: '1342273' },
      { rxId: '1342272' },
      { rxId: '1342274' },
      { rxId: '1342276' },
      { rxId: '1342278' },
      { rxId: '1342285' },
      { rxId: '1342280' },
      { rxId: '1342279' },
      { rxId: '1342281' },
      { rxId: '1342283' },
      { rxId: '1342287' },
      { rxId: '1342288' },
      { rxId: '1342289' },
      { rxId: '1342292' },
      { rxId: '1342303' },
      { rxId: '1342326' },
      { rxId: '1342328' },
      { rxId: '1342327' },
      { rxId: '1342405' },
      { rxId: '1339730' },
      { rxId: '1342264' },
      { rxId: '1342653' },
      { rxId: '1342266' },
      { rxId: '1340023' },
      { rxId: '1342793' },
      { rxId: '1342830' },
      { rxId: '1342838' },
      { rxId: '1342843' },
      { rxId: '1342608' },
      { rxId: '1342604' },
      { rxId: '1342599' },
      { rxId: '1342406' },
      { rxId: '1341638' },
      { rxId: '1339931' },
      { rxId: '1354447' },
      { rxId: '1354454' },
      { rxId: '1339957' },
      { rxId: '1339373' },
      { rxId: '1341515' },
      { rxId: '1341422' },
      { rxId: '1342634' },
      { rxId: '1342275' },
      { rxId: '1341797' },
    ];
  }

  /**
   * Convert M/D/YY date format to YYYY-MM-DD
   */
  private formatDateOfBirth(dobStr: string): string {
    const [month, day, year] = dobStr.split('/');
    const yearInt = parseInt(year, 10);

    // Determine century based on 2-digit year
    const fullYear = yearInt >= 30 ? 1900 + yearInt : 2000 + yearInt;

    // Pad month and day with zeros if needed
    const paddedMonth = month.padStart(2, '0');
    const paddedDay = day.padStart(2, '0');

    return `${fullYear}-${paddedMonth}-${paddedDay}`;
  }

  /**
   * Find pharmacy integration records using the patient list
   */
  private async findPharmacyIntegrationRecords(
    additionalIds?: string[],
  ): Promise<ExtendedPharmacyIntegration[]> {
    const patientList = this.getPatientList();

    // Separate patients with RXID from those without
    const patientsWithRxId = patientList.filter((p) => 'rxId' in p);
    const patientsWithoutRxId = patientList.filter((p) => !('rxId' in p));

    const allIds: string[] = [];

    // First, find records by RXID if any patients have it
    if (patientsWithRxId.length > 0) {
      const rxIds = patientsWithRxId.map((p) => (p as { rxId: string }).rxId);
      const recordsByRxId = await this.prisma.$queryRawUnsafe<
        Array<{ id: string }>
      >(
        `SELECT DISTINCT pi.id
         FROM "PharmacyIntegration" pi
         WHERE pi."pharmacyId" = $1
         AND pi.response->>'PRESCRIPTIONRXID' = ANY($2::text[])
         ORDER BY pi.id`,
        this.RED_ROCK_PHARMACY_ID,
        rxIds,
      );
      allIds.push(...recordsByRxId.map((r) => r.id));
    }

    // Then, find records by patient name and DOB for those without RXID
    if (patientsWithoutRxId.length > 0) {
      // Build WHERE clause conditions as strings
      const conditions = patientsWithoutRxId.map((patient) => {
        const p = patient as {
          lastName: string;
          firstName: string;
          dob: string;
        };
        const patientName = `${p.lastName}, ${p.firstName}`;
        const formattedDob = this.formatDateOfBirth(p.dob);

        // Escape single quotes in names
        const escapedName = patientName.replace(/'/g, "''");

        return `(pi.response->>'PATIENTNAME' = '${escapedName}' AND pi.response->>'PATIENTDOB' = '${formattedDob}')`;
      });

      const whereClause = conditions.join(' OR ');

      const recordsByPatient = await this.prisma.$queryRawUnsafe<
        Array<{ id: string }>
      >(
        `SELECT DISTINCT pi.id
         FROM "PharmacyIntegration" pi
         WHERE pi."pharmacyId" = $1
         AND (${whereClause})
         ORDER BY pi.id`,
        this.RED_ROCK_PHARMACY_ID,
      );
      allIds.push(...recordsByPatient.map((r) => r.id));
    }

    // Add records by prescription RX IDs if provided
    if (additionalIds && additionalIds.length > 0) {
      // Query for records by PRESCRIPTIONRXID from the response
      const additionalRecords = await this.prisma.$queryRawUnsafe<
        Array<{ id: string }>
      >(
        `SELECT DISTINCT pi.id
         FROM "PharmacyIntegration" pi
         WHERE pi."pharmacyId" = $1
         AND pi.response->>'PRESCRIPTIONRXID' = ANY($2::text[])
         ORDER BY pi.id`,
        this.RED_ROCK_PHARMACY_ID,
        additionalIds,
      );

      const additionalRecordIds = additionalRecords.map((r) => r.id);
      allIds.push(...additionalRecordIds);
    }

    // Fetch full records with relationships
    const records = await this.prisma.pharmacyIntegration.findMany({
      where: {
        id: { in: allIds },
        responseStatus: { not: 0 }, // Skip already resent records
      },
      include: {
        prescription: {
          include: {
            treatment: true,
            patient: {
              include: {
                user: true,
                state: true,
                shippingAddresses: {
                  include: {
                    state: true,
                  },
                  where: {
                    default: true,
                  },
                  take: 1,
                },
              },
            },
            doctor: {
              include: {
                user: true,
                state: true,
                prescribesIn: true,
              },
            },
            productPrice: {
              include: {
                product: {
                  include: {
                    pharmacy: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return records as ExtendedPharmacyIntegration[];
  }

  /**
   * Build prescription request matching the treatment prescribe event logic
   */
  private async buildPrescriptionRequest(
    record: ExtendedPharmacyIntegration,
  ): Promise<PrescriptionRequest | null> {
    try {
      const { prescription } = record;
      const { patient, doctor, treatment } = prescription;

      // Extract treatment context and external mapping
      const treatmentState = treatment.state as any;
      const context = treatmentState?.context as TreatmentMachineContext;

      if (!context?.externalMapping) {
        console.error(
          `No external mapping found in treatment ${treatment.id} context`,
        );
        return null;
      }

      // Get the product mapping that was used
      const selectedMapping = await this.prisma.productPriceMapping.findUnique({
        where: { id: context.externalMapping },
        include: {
          productPrice: {
            include: {
              product: {
                include: {
                  pharmacy: true,
                },
              },
            },
          },
        },
      });

      if (!selectedMapping) {
        console.error(`External mapping ${context.externalMapping} not found`);
        return null;
      }

      // Extract allergies from onboarding state
      const onboardingState = patient.onboardingState as any;
      const allergies =
        onboardingState?.context?.questionnaire?.allergies || [];

      // Build patient data
      const pharmacyPatient: PharmacyPatient = {
        id: patient.id,
        firstName: patient.user.firstName,
        lastName: patient.user.lastName,
        dateOfBirth: patient.birthDate
          ? new Date(patient.birthDate).toISOString().split('T')[0]
          : '',
        gender: patient.gender || 'unknown',
        address: {
          street1: patient.shippingAddresses[0]?.address1 || '',
          street2: patient.shippingAddresses[0]?.address2 || undefined,
          city: patient.shippingAddresses[0]?.city || '',
          state: patient.shippingAddresses[0]?.state?.code || '',
          zipCode: patient.shippingAddresses[0]?.zip || '',
          country: 'US',
        },
        phoneNumber: patient.user.phone || '',
        email: patient.user.email || undefined,
        knownAllergies: allergies,
      };

      // Get prescriber license for patient's state
      const prescriberLicense = doctor.prescribesIn.find(
        (p) => p.stateId === patient.stateId,
      );

      // Build prescriber data
      const pharmacyPrescriber: PharmacyPrescriber = {
        id: doctor.id,
        npi: doctor.npiNumber || '',
        firstName: doctor.user.firstName,
        lastName: doctor.user.lastName,
        stateLicenseNumber: prescriberLicense?.licenseNumber || undefined,
        address: {
          street1: doctor.address1,
          street2: doctor.address2 || undefined,
          city: doctor.city,
          state: doctor.state.code || '',
          zipCode: doctor.zip,
          country: 'US',
        },
        phoneNumber: doctor.primaryPhone || undefined,
      };

      // Fetch ALL mappings for this product price
      const allMappings = await this.prisma.productPriceMapping.findMany({
        where: {
          productPriceId: prescription.productPriceId,
        },
      });

      if (allMappings.length === 0) {
        console.error(
          `No ProductPriceMappings found for product ${prescription.productPriceId}`,
        );
        return null;
      }

      // Build product data from ALL mappings (Red Rock service will filter by store ID)
      const pharmacyProducts: PharmacyPrescriptionProduct[] = allMappings.map(
        (mapping) => {
          const mappingMetadata = mapping.metadata as any;
          const activeProduct = context.activeProduct;

          return {
            id: prescription.productPriceId,
            name: mapping.name || activeProduct?.name || '',
            dose: activeProduct?.dose,
            form: activeProduct?.form?.toString() || '',
            externalId: mapping.externalId,
            drugDescription: mapping.name || activeProduct?.name || '',
            quantity: Number(mappingMetadata?.quantity) || 1,
            daysSupply:
              Number(mappingMetadata?.daysSupply) || activeProduct?.days || 28,
            sig:
              mappingMetadata?.directions ||
              mappingMetadata?.sig ||
              'Take as directed',
            refills: 0,
            originalProductDetails: activeProduct,
            metadata: mappingMetadata,
          };
        },
      );

      const prescriptionRequest: PrescriptionRequest = {
        treatmentId: prescription.treatmentId,
        prescriptionId: prescription.id,
        patient: pharmacyPatient,
        prescriber: pharmacyPrescriber,
        products: pharmacyProducts,
        pharmacyIdentifier:
          selectedMapping.productPrice?.product?.pharmacy?.slug || 'redRock',
        prescriptionIssueDate: new Date(prescription.createdAt),
        originalTreatmentDetails: treatment,
      };

      return prescriptionRequest;
    } catch (error) {
      console.error(`Failed to build prescription request: ${error.message}`);
      return null;
    }
  }

  async run(
    _: string[],
    options?: {
      dryRun?: boolean;
      additionalIds?: string[];
      limit?: number;
      delay?: number;
      test?: number;
    },
  ): Promise<void> {
    const isDryRun = options?.dryRun || false;
    const additionalIds = options?.additionalIds || [];
    const limit = options?.limit;
    const delay = options?.delay || 1000; // Default 1000ms delay
    const testIndex = options?.test;
    const csvRecords: Array<{ originalOrderId: string; newOrderId: string }> =
      [];

    console.log('🔍 Searching for Red Rock prescriptions to resend...\n');

    // Find all pharmacy integration records for the specified patients
    const allRecords = await this.findPharmacyIntegrationRecords(additionalIds);

    if (allRecords.length === 0) {
      console.log('✅ No prescriptions found to resend for Red Rock.');
      return;
    }

    console.log(
      `##### Found ${allRecords.length} prescriptions sent to Red Rock\n`,
    );

    // Test mode: process only the selected record
    if (testIndex !== undefined) {
      if (testIndex >= allRecords.length) {
        console.log(
          `❌ Test index ${testIndex} is out of range. Valid range: 0-${allRecords.length - 1}`,
        );
        return;
      }

      console.log(`\n🧪 TEST MODE: Processing record at index ${testIndex}\n`);
      await this.runTestMode(allRecords[testIndex], testIndex);
      return;
    }

    if (limit && allRecords.length > limit) {
      console.log(
        `\n🔧 Limited to processing ${limit} prescriptions for this run.`,
      );
      allRecords.splice(limit);
    }

    console.log(
      isDryRun
        ? '\n🏃‍♂️ Dry run mode - showing what would be resent without sending to API...\n'
        : '\n🚀 Starting to resend prescriptions to Red Rock...\n',
    );

    // Get the Red Rock pharmacy service
    const redRockPharmacyService =
      this.pharmacyServiceFactory.getService('redRock');

    let successCount = 0;
    let errorCount = 0;

    for (const record of allRecords) {
      try {
        const response = record.response as any;
        const patientName = response?.PATIENTNAME || 'Unknown';
        console.log(
          `\n📤 Processing prescription ${record.prescriptionId} for ${patientName}...`,
        );

        // Build the prescription request
        const prescriptionRequest = await this.buildPrescriptionRequest(record);

        if (!prescriptionRequest) {
          console.log(
            `  ❌ Could not build prescription request for ${record.prescriptionId}`,
          );
          errorCount++;
          continue;
        }

        if (isDryRun) {
          // In dry run mode, just show what would be sent
          console.log(
            `  ✅ Would resend prescription ${record.prescriptionId} for ${patientName}`,
          );
          console.log(`     Treatment: ${prescriptionRequest.treatmentId}`);
          prescriptionRequest.products.forEach((product, idx) => {
            console.log(`     Product ${idx + 1}: ${product.name}`);
            console.log(`       - External ID: ${product.externalId}`);
            console.log(`       - Quantity: ${product.quantity}`);
            console.log(`       - Days Supply: ${product.daysSupply}`);
            console.log(`       - Sig: ${product.sig}`);
          });
          successCount++;
        } else {
          // Actually send the request
          const submitRequest: MultiplePrescriptionRequest = {
            prescriptions: [prescriptionRequest],
          };

          const result =
            await redRockPharmacyService.submitPrescriptions(submitRequest);

          if (result.success && result.results[0]?.success) {
            console.log(
              `  ✅ Successfully resent prescription ${record.prescriptionId}`,
            );

            // Update original PharmacyIntegration record with responseStatus = 0
            await this.prisma.pharmacyIntegration.update({
              where: { id: record.id },
              data: { responseStatus: 0 },
            });

            // Track successful resend for CSV
            csvRecords.push({
              originalOrderId: record.orderId,
              newOrderId:
                result.results[0].pharmacyOrderId || `${record.orderId}-resent`,
            });

            successCount++;

            // Add delay between requests (skip on last item)
            if (allRecords.indexOf(record) < allRecords.length - 1) {
              await this.sleep(delay);
            }
          } else {
            const errorMessage =
              result.results[0]?.message || result.message || 'Unknown error';
            console.log(
              `  ❌ Failed to resend prescription ${record.prescriptionId}: ${errorMessage}`,
            );
            errorCount++;
          }
        }
      } catch (error) {
        console.log(
          `  ❌ Failed to resend prescription ${record.prescriptionId}: ${error.message}`,
        );
        errorCount++;
      }
    }

    console.log(
      isDryRun ? '\n🎯 Dry run complete:' : '\n🎯 Resending complete:',
    );
    console.log(
      isDryRun
        ? `  ✅ Would resend: ${successCount} prescriptions`
        : `  ✅ Successfully resent: ${successCount} prescriptions`,
    );
    console.log(
      isDryRun
        ? `  ❌ Would fail: ${errorCount} prescriptions`
        : `  ❌ Failed to resend: ${errorCount} prescriptions`,
    );

    // Generate CSV file if there were successful resends and not in dry run mode
    if (!isDryRun && csvRecords.length > 0) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const csvFilename = `redrock-resent-prescriptions-${timestamp}.csv`;

      // Create CSV content with headers
      const csvContent = [
        'Original Order ID,New Order ID',
        ...csvRecords.map(
          (record) => `${record.originalOrderId},${record.newOrderId}`,
        ),
      ].join('\n');

      // Write CSV file
      try {
        writeFileSync(csvFilename, csvContent);
        console.log(`\n📄 CSV file generated: ${csvFilename}`);
      } catch (error) {
        console.log(`\n❌ Failed to write CSV file: ${error.message}`);
      }
    }
  }

  /**
   * Run test mode for a specific record
   */
  private async runTestMode(
    record: ExtendedPharmacyIntegration,
    index: number,
  ): Promise<void> {
    try {
      const response = record.response as any;
      const patientName = response?.PATIENTNAME || 'Unknown';

      console.log(`Record Index: ${index}`);
      console.log(`Patient: ${patientName}`);
      console.log(`Prescription ID: ${record.prescriptionId}`);
      console.log(`Treatment ID: ${record.prescription.treatmentId}`);
      console.log(`Original Order ID: ${record.orderId}\n`);

      // Build the prescription request
      const prescriptionRequest = await this.buildPrescriptionRequest(record);

      if (!prescriptionRequest) {
        console.log('❌ Could not build prescription request');
        return;
      }

      // Display prescription details
      console.log('\n📋 Prescription Details:');
      console.log(
        `  Patient: ${prescriptionRequest.patient.firstName} ${prescriptionRequest.patient.lastName}`,
      );
      console.log(`  DOB: ${prescriptionRequest.patient.dateOfBirth}`);
      console.log(
        `  Patient State: ${prescriptionRequest.patient.address.state}`,
      );
      console.log(
        `  Doctor: ${prescriptionRequest.prescriber.firstName} ${prescriptionRequest.prescriber.lastName}`,
      );
      console.log(`  Products: ${prescriptionRequest.products.length}`);

      // For each product, generate the prescription image
      for (let i = 0; i < prescriptionRequest.products.length; i++) {
        const product = prescriptionRequest.products[i];

        console.log(`\n📋 Processing Product ${i + 1}: ${product.name}`);
        console.log(`  External ID: ${product.externalId}`);
        console.log(`  Quantity: ${product.quantity}`);
        console.log(`  Days Supply: ${product.daysSupply}`);
        console.log(`  Sig: ${product.sig}`);

        // Create prescriber data with Red Rock's hardcoded values
        const requestWithPrescriberData = {
          ...prescriptionRequest,
          prescriber: {
            ...prescriptionRequest.prescriber,
            address: {
              street1: 'Wilmington',
              city: 'Wilmington',
              state: 'DE',
              zipCode: '19801',
            },
            phoneNumber: '(*************',
          },
        };

        try {
          // Generate prescription image
          console.log('  🖼️ Generating prescription image...');
          const prescriptionImageBase64 =
            await this.prescriptionImageGenerator.generatePrescriptionImage(
              requestWithPrescriberData,
              {
                ...product,
                quantity: product.metadata?.quantity || product.quantity,
              },
            );

          // Save the image to file
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const filename = `redrock-prescription-test-${index}-product-${i + 1}-${timestamp}.png`;
          const filepath = path.join(process.cwd(), filename);

          // Convert base64 to buffer and save
          const imageBuffer = Buffer.from(prescriptionImageBase64, 'base64');
          writeFileSync(filepath, imageBuffer);

          console.log(`  ✅ Image saved to: ${filename}`);

          // Show what fields would be sent in the rebuilt request
          console.log('\n  📄 Key Fields in Rebuilt Request:');
          console.log(
            `    PRESCRIPTIONTRANDATE: ${prescriptionRequest.prescriptionIssueDate?.toISOString().split('T')[0]}`,
          );
          console.log(
            `    PATIENTNAME: ${prescriptionRequest.patient.lastName}, ${prescriptionRequest.patient.firstName}`,
          );
          console.log(
            `    PATIENTDOB: ${prescriptionRequest.patient.dateOfBirth}`,
          );
          console.log(`    DRUGID: ${product.externalId}`);
          console.log(`    PRESCRIPTONORIGINALQTY: ${product.quantity}`);
          console.log(`    PRESCRIPTIONSIGCODE: ${product.sig}`);
          console.log(`    PRESCRIPTIONDAYSSUPPLY: ${product.daysSupply}`);
          console.log(
            `    Store ID from metadata: ${product.metadata?.storeId || 'Not found'}`,
          );
        } catch (error) {
          console.error(
            `  ❌ Failed to generate image for product ${i + 1}: ${error.message}`,
          );
        }
      }

      // Display original request from the PharmacyIntegration record
      console.log('\n📄 Original Request (from PharmacyIntegration):');
      const originalRequest = record.request as any;
      const originalForDisplay = JSON.parse(JSON.stringify(originalRequest));
      if (originalForDisplay.Field && Array.isArray(originalForDisplay.Field)) {
        originalForDisplay.Field = originalForDisplay.Field.map(
          (field: string) => {
            if (field.startsWith('PRESCRIPTIONATTACHMENTBASE64:')) {
              return 'PRESCRIPTIONATTACHMENTBASE64:<BASE64_IMAGE_REMOVED>';
            }
            return field;
          },
        );
      }
      console.log(JSON.stringify(originalForDisplay, null, 2));

      console.log('\n📄 Original Response:');
      console.log(JSON.stringify(record.response, null, 2));
    } catch (error) {
      console.error(`Failed to run test mode: ${error.message}`);
      console.error(error.stack);
    }
  }
}
