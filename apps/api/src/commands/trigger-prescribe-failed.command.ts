import * as fs from 'fs';
import confirm from '@inquirer/confirm';
import input from '@inquirer/input';
import search from '@inquirer/search';
import select from '@inquirer/select';
import { PrismaService } from '@modules/prisma/prisma.service';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { Injectable, Logger } from '@nestjs/common';
import * as chalk from 'chalk';
import { parse } from 'csv-parse';
import * as dayjs from 'dayjs';
import { Command, CommandRunner, Option } from 'nest-commander';
import * as ora from 'ora';

interface TreatmentOption {
  id: string;
  status: string;
  productName: string;
  pharmacyName: string;
  dosageLabel?: string;
  currentRefill: number;
  refills: number;
  createdAt: Date;
  state: any;
  displayName: string;
}

interface ProcessPatientResult {
  totalTreatments: number;
  successful: number;
  failed: number;
  errors: Array<{ treatmentId: string; error: string }>;
}

@Injectable()
@Command({
  name: 'trigger-prescribe-failed',
  description:
    'Trigger prescribeFailed event for stuck treatments in prescribing state',
})
export class TriggerPrescribeFailedCommand extends CommandRunner {
  private readonly logger = new Logger(TriggerPrescribeFailedCommand.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly treatmentService: TreatmentService,
  ) {
    super();
  }

  @Option({
    flags: '--email [email]',
    description: 'Patient email (skips email input)',
  })
  parseEmail(val: string): string {
    return val;
  }

  @Option({
    flags: '--patient-id [patientId]',
    description: 'Patient ID (skips patient selection)',
  })
  parsePatientId(val: string): string {
    return val;
  }

  @Option({
    flags: '--treatment-id [treatmentId]',
    description:
      'Treatment ID to trigger event for (skips treatment selection)',
  })
  parseTreatmentId(val: string): string {
    return val;
  }

  @Option({
    flags: '--csv-file [csvFile]',
    description: 'CSV file path with patient IDs and dates (format: id,date)',
  })
  parseCsvFile(val: string): string {
    return val;
  }

  @Option({
    flags: '--batch-size [batchSize]',
    description: 'Number of patients to process in parallel (default: 5)',
  })
  parseBatchSize(val: string): number {
    const size = parseInt(val, 10);
    if (isNaN(size) || size < 1) {
      return 5; // default
    }
    return size;
  }

  async run(
    _: string[],
    options?: {
      email?: string;
      patientId?: string;
      treatmentId?: string;
      csvFile?: string;
      batchSize?: number;
    },
  ): Promise<void> {
    try {
      // Handle CSV file processing
      if (options?.csvFile) {
        if (!fs.existsSync(options.csvFile)) {
          throw new Error(`CSV file not found: ${options.csvFile}`);
        }

        const batchSize = options.batchSize || 5;
        await this.processCsvFile(options.csvFile, batchSize);
        return;
      }

      const isInteractive =
        (!options?.email && !options?.patientId) || !options?.treatmentId;

      // 1. Get patient by identifier
      let patientIdentifier: { type: 'email' | 'id'; value: string };

      if (options?.email) {
        patientIdentifier = { type: 'email', value: options.email };
      } else if (options?.patientId) {
        patientIdentifier = { type: 'id', value: options.patientId };
      } else {
        patientIdentifier = await this.getPatientIdentifier();
      }

      const patient = await this.findPatient(
        patientIdentifier.type,
        patientIdentifier.value,
      );

      if (!patient) {
        throw new Error(
          `Patient with ${patientIdentifier.type} ${patientIdentifier.value} not found`,
        );
      }

      console.log(
        chalk.green(
          `✅ Found patient: ${patient.user.firstName} ${patient.user.lastName}`,
        ),
      );

      // 2. Get treatments for patient
      const treatments = await this.getPatientTreatments(patient.id);

      if (treatments.length === 0) {
        console.log(chalk.yellow('⚠️  No treatments found for this patient.'));
        return;
      }

      // 3. Choose processing mode (single vs all treatments)
      let processAll = false;
      let selectedTreatment: TreatmentOption | null = null;

      if (options?.treatmentId) {
        // Specific treatment ID provided via flag
        selectedTreatment = treatments.find(
          (t) => t.id === options.treatmentId,
        );
        if (!selectedTreatment) {
          throw new Error('Selected treatment not found');
        }
      } else if (treatments.length === 1) {
        // Only one treatment - process it directly
        selectedTreatment = treatments[0];
      } else {
        // Multiple treatments - give user choice
        const processingChoice = await select({
          message: `Found ${treatments.length} stuck treatments. How would you like to proceed?`,
          choices: [
            {
              name: `Process ALL ${treatments.length} treatments`,
              value: 'all',
            },
            {
              name: 'Select a specific treatment',
              value: 'single',
            },
          ],
        });

        if (processingChoice === 'all') {
          processAll = true;
        } else {
          selectedTreatment = await this.selectTreatment(treatments);
          if (!selectedTreatment) {
            throw new Error('Selected treatment not found');
          }
        }
      }

      // 4. Show confirmation
      if (isInteractive) {
        const confirmed = processAll
          ? await this.showAllTreatmentsConfirmation(patient, treatments)
          : await this.showConfirmation(patient, selectedTreatment!);

        if (!confirmed) {
          console.log(chalk.yellow('Operation cancelled.'));
          return;
        }
      }

      // 5. Process treatment(s)
      if (processAll) {
        // Process all treatments using the updated processPatient method
        const result = await this.processPatient(patient.id);

        console.log(
          chalk.green(
            `✅ Processed ${result.totalTreatments} treatments: ${result.successful} successful, ${result.failed} failed`,
          ),
        );

        if (result.errors.length > 0) {
          console.log(chalk.red('\n=== TREATMENT ERRORS ==='));
          result.errors.forEach((err) => {
            console.log(
              chalk.red(`Treatment ${err.treatmentId}: ${err.error}`),
            );
          });
        }
      } else {
        // Process single treatment
        await this.triggerPrescribeFailedAndPrescribedEvents(
          selectedTreatment!.id,
        );
        console.log(
          chalk.green(
            `✅ Successfully auto-prescribed treatment ${selectedTreatment!.id} and moved to waiting between refills`,
          ),
        );
      }

      // 6. Optional refill rescheduling
      let newRefillDate: string | null = null;
      if (isInteractive) {
        const shouldReschedule = await confirm({
          message: 'Would you like to reschedule the refill dates?',
          default: false,
        });

        if (shouldReschedule) {
          if (processAll) {
            // For all treatments, get one date and apply to all
            newRefillDate = await this.getNewRefillDate();
            if (newRefillDate) {
              const result = await this.processPatient(
                patient.id,
                newRefillDate,
              );
              console.log(
                chalk.green(
                  `✅ Rescheduled refills for ${result.successful} treatments`,
                ),
              );
            }
          } else {
            newRefillDate = await this.rescheduleRefill(selectedTreatment!.id);
          }
        }
      }

      // 7. Show non-interactive command if run interactively (only for single treatment)
      if (isInteractive && !processAll && selectedTreatment) {
        this.showNonInteractiveCommand(
          patientIdentifier,
          selectedTreatment.id,
          newRefillDate,
        );
      }
    } catch (error) {
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  }

  private async processCsvFile(
    csvFile: string,
    batchSize: number = 5,
  ): Promise<void> {
    const spinner = ora('Reading CSV file...').start();
    const records: Array<{ id: string; date?: string }> = [];

    // Read and parse CSV
    await new Promise<void>((resolve, reject) => {
      fs.createReadStream(csvFile)
        .pipe(parse({ columns: true, skip_empty_lines: true }))
        .on('data', (row: any) => {
          if (row.id) {
            records.push({
              id: row.id.trim(),
              date: row.date?.trim() || undefined,
            });
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    spinner.succeed(`Loaded ${records.length} patient records from CSV`);

    // Process statistics
    let totalPatients = 0;
    let totalTreatments = 0;
    let successfulTreatments = 0;
    let failedTreatments = 0;
    const errors: Array<{
      patientId: string;
      treatmentId?: string;
      error: string;
    }> = [];

    // Process in batches
    const processingSpinner = ora('Processing patients...').start();

    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, Math.min(i + batchSize, records.length));

      await Promise.all(
        batch.map(async (record) => {
          try {
            const result = await this.processPatient(record.id, record.date);
            totalPatients++;
            totalTreatments += result.totalTreatments;
            successfulTreatments += result.successful;
            failedTreatments += result.failed;

            // Add treatment-specific errors
            result.errors.forEach((error) => {
              errors.push({
                patientId: record.id,
                treatmentId: error.treatmentId,
                error: error.error,
              });
            });
          } catch (error: any) {
            totalPatients++;
            failedTreatments++;
            errors.push({
              patientId: record.id,
              error: error.message,
            });
          }

          processingSpinner.text = `Processing patients... (${totalPatients}/${records.length} patients, ${successfulTreatments + failedTreatments} treatments processed)`;
        }),
      );
    }

    processingSpinner.succeed(
      `Processed ${totalPatients} patients with ${totalTreatments} treatments: ${successfulTreatments} successful, ${failedTreatments} failed`,
    );

    // Display errors if any
    if (errors.length > 0) {
      console.log(chalk.red('\n=== ERRORS ==='));
      errors.forEach((err) => {
        const errorMessage = err.treatmentId
          ? `Patient ${err.patientId}, Treatment ${err.treatmentId}: ${err.error}`
          : `Patient ${err.patientId}: ${err.error}`;
        console.log(chalk.red(errorMessage));
      });
    }

    // Write error report to file
    if (errors.length > 0) {
      const errorFile = `auto-prescribe-errors-${Date.now()}.json`;
      fs.writeFileSync(errorFile, JSON.stringify(errors, null, 2));
      console.log(chalk.yellow(`\nError report saved to: ${errorFile}`));
    }
  }

  private async processPatient(
    patientId: string,
    newRefillDate?: string,
  ): Promise<ProcessPatientResult> {
    // Find patient
    const patient = await this.findPatient('id', patientId);
    if (!patient) {
      throw new Error(`Patient not found`);
    }

    // Get treatments
    const treatments = await this.getPatientTreatments(patient.id);
    if (treatments.length === 0) {
      throw new Error('No treatments found stuck in prescribing state');
    }

    const result: ProcessPatientResult = {
      totalTreatments: treatments.length,
      successful: 0,
      failed: 0,
      errors: [],
    };

    console.log(
      `Processing ${treatments.length} stuck treatments for patient ${patientId}...`,
    );

    // Process ALL treatments
    for (const treatment of treatments) {
      try {
        // Auto-prescribe treatment (prescribeFailed -> prescribed)
        await this.triggerPrescribeFailedAndPrescribedEvents(treatment.id);

        console.log(
          `✅ Successfully auto-prescribed treatment ${treatment.id} (${treatment.productName})`,
        );

        // Reschedule if date provided
        if (newRefillDate) {
          await this.rescheduleRefillForBatch(treatment.id, newRefillDate);
          console.log(
            `📅 Rescheduled refill for treatment ${treatment.id} to ${newRefillDate}`,
          );
        }

        result.successful++;
      } catch (error: any) {
        console.log(
          `❌ Failed to process treatment ${treatment.id} (${treatment.productName}): ${error.message}`,
        );
        result.failed++;
        result.errors.push({
          treatmentId: treatment.id,
          error: error.message,
        });
      }
    }

    return result;
  }

  private async rescheduleRefillForBatch(
    treatmentId: string,
    dateString: string,
  ): Promise<void> {
    // Parse the date in M/D/YYYY format
    let scheduledDate = dayjs(dateString, 'M/D/YYYY');

    if (!scheduledDate.isValid()) {
      throw new Error(`Invalid date format: ${dateString}`);
    }

    const oneHourFromNow = dayjs().add(1, 'hour');

    // If date is less than 1 hour from now, adjust it
    if (scheduledDate.isBefore(oneHourFromNow)) {
      scheduledDate = oneHourFromNow;
    }

    const finalDate = scheduledDate.toISOString();

    // Get the treatment actor and trigger the moveRefillDate event
    const actor = await this.treatmentService.getActor(treatmentId, () => {});

    if (!actor.getSnapshot().can({ type: 'moveRefillDate', date: finalDate })) {
      throw new Error('Treatment refill date cannot be moved in current state');
    }

    actor.send({ type: 'moveRefillDate', date: finalDate });
    await this.treatmentService.updateTreatmentRecord(actor);
  }

  private async getPatientIdentifier(): Promise<{
    type: 'email' | 'id';
    value: string;
  }> {
    const identifierType = await search({
      message: 'Search patient by:',
      source: async () => [
        { name: 'Email address', value: 'email' },
        { name: 'Patient ID', value: 'id' },
      ],
    });

    if (identifierType === 'email') {
      const email = await input({
        message: 'Enter patient email:',
        validate: (input: string) => {
          if (!input?.trim()) {
            return 'Patient email is required';
          }
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(input.trim())) {
            return 'Please enter a valid email address';
          }
          return true;
        },
      });
      return { type: 'email', value: email.trim() };
    } else {
      const patientId = await input({
        message: 'Enter patient ID:',
        validate: (input: string) => {
          if (!input?.trim()) {
            return 'Patient ID is required';
          }
          return true;
        },
      });
      return { type: 'id', value: patientId.trim() };
    }
  }

  private async findPatient(type: 'email' | 'id', value: string) {
    const where = type === 'email' ? { user: { email: value } } : { id: value };

    return await this.prismaService.patient.findFirst({
      where,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  private extractStatePath(value: any): string {
    if (typeof value === 'string') return value;
    if (typeof value === 'object' && value !== null) {
      const entries = Object.entries(value);
      if (entries.length === 1) {
        const [key, val] = entries[0];
        return val ? `${key}.${this.extractStatePath(val)}` : key;
      }
      return Object.keys(value).join('.');
    }
    return '';
  }

  private async getPatientTreatments(
    patientId: string,
  ): Promise<TreatmentOption[]> {
    const treatments = await this.prismaService.treatment.findMany({
      where: {
        patientId: patientId,
        deletedAt: null,
        // Filter for treatments stuck in prescribing state
        status: 'inProgress.prescribing',
      },
      include: {
        initialProductPrice: {
          include: {
            product: {
              include: {
                pharmacy: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return treatments.map((treatment) => {
      const state = treatment.state as any;
      const productName = treatment.initialProductPrice.product.name;
      const pharmacyName = treatment.initialProductPrice.product.pharmacy.name;
      const dosageLabel = treatment.initialProductPrice.dosageLabel;

      // Extract current state for display
      const currentState = state?.value
        ? this.extractStatePath(state.value)
        : treatment.status;

      const displayName = `${productName} | Refill ${treatment.currentRefill}/${treatment.refills} | ${currentState}${dosageLabel ? ` | ${dosageLabel}` : ''} | Created: ${treatment.createdAt.toLocaleDateString()}`;

      return {
        id: treatment.id,
        status: treatment.status,
        productName,
        pharmacyName,
        dosageLabel,
        currentRefill: treatment.currentRefill,
        refills: treatment.refills,
        createdAt: treatment.createdAt,
        state,
        displayName,
      };
    });
  }

  private async selectTreatment(
    treatments: TreatmentOption[],
  ): Promise<TreatmentOption | null> {
    if (treatments.length === 1) {
      console.log(
        chalk.blue(`Found 1 treatment: ${treatments[0].displayName}`),
      );
      return treatments[0];
    }

    console.log(
      chalk.blue(`Found ${treatments.length} treatments for this patient.`),
    );

    const selectedTreatmentId = await search({
      message: 'Select a treatment (type to search):',
      source: async (input: string) => {
        const filtered = treatments.filter((option) =>
          option.displayName.toLowerCase().includes(input?.toLowerCase() || ''),
        );
        return filtered.map((option) => ({
          name: option.displayName,
          value: option.id,
        }));
      },
    });

    return treatments.find((t) => t.id === selectedTreatmentId) || null;
  }

  private async showConfirmation(
    patient: any,
    treatment: TreatmentOption,
  ): Promise<boolean> {
    console.log('\n' + chalk.cyan('=== CONFIRMATION ==='));
    console.log(
      `${chalk.bold('Patient:')} ${patient.user.firstName} ${patient.user.lastName}`,
    );
    console.log(`${chalk.bold('Email:')} ${patient.user.email}`);
    console.log(`${chalk.bold('Patient ID:')} ${patient.id}`);

    // Create a user-friendly treatment display
    const treatmentDisplay = `${treatment.productName} (Refill ${treatment.currentRefill}/${treatment.refills})`;
    console.log(`${chalk.bold('Treatment:')} ${treatmentDisplay}`);

    if (treatment.dosageLabel) {
      console.log(`${chalk.bold('Dosage:')} ${treatment.dosageLabel}`);
    }

    // Show the actual state machine state
    if (treatment.state?.value) {
      const currentState = this.extractStatePath(treatment.state.value);
      console.log(`${chalk.bold('Current State:')} ${currentState}`);
    } else {
      console.log(`${chalk.bold('Current Status:')} ${treatment.status}`);
    }

    console.log(
      `${chalk.bold('Created:')} ${treatment.createdAt.toLocaleDateString()}`,
    );

    console.log(
      '\n' +
        chalk.yellow(
          'This will mark the treatment as prescribed (after prescribe failure) and move it to waitingBetweenRefills state.',
        ),
    );
    console.log('\n');

    return await confirm({
      message: `Mark treatment as prescribed and move to waiting between refills?`,
      default: false,
    });
  }

  private async triggerPrescribeFailedAndPrescribedEvents(
    treatmentId: string,
  ): Promise<void> {
    try {
      // Get the treatment actor
      const actor = await this.treatmentService.getActor(treatmentId, (e) =>
        this.logger.verbose(
          `[TriggerPrescribeFailedAndPrescribedEvents] ${treatmentId}:`,
          e,
        ),
      );

      // Send the prescribeFailed event first (prescribing -> waitingForPrescription)
      actor.send({ type: 'prescribeFailed' });
      this.logger.log(
        `Triggered prescribeFailed event for treatment ${treatmentId} - transitioned to waitingForPrescription`,
      );

      // Immediately send the prescribed event (waitingForPrescription -> waitingBetweenRefills)
      actor.send({ type: 'prescribed' });
      this.logger.log(
        `Triggered prescribed event for treatment ${treatmentId} - transitioned to waitingBetweenRefills`,
      );

      // Update the treatment record
      await this.treatmentService.updateTreatmentRecord(actor);

      this.logger.log(
        `Successfully auto-prescribed treatment ${treatmentId} after prescribe failure`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to trigger prescribe events for treatment ${treatmentId}:`,
        error,
      );
      throw error;
    }
  }

  private async rescheduleRefill(treatmentId: string): Promise<string> {
    const newRefillDate = await input({
      message: 'Enter new refill date (M/D/YYYY):',
      validate: (input: string) => {
        if (!input?.trim()) {
          return 'Date is required';
        }

        const date = dayjs(input.trim(), 'M/D/YYYY');
        if (!date.isValid()) {
          return 'Please enter a valid date in M/D/YYYY format (e.g., 12/25/2024)';
        }

        const oneHourFromNow = dayjs().add(1, 'hour');
        if (date.isBefore(oneHourFromNow)) {
          return 'Date must be at least 1 hour in the future. It will be automatically adjusted.';
        }

        return true;
      },
    });

    try {
      // Parse the input date and adjust if necessary
      let scheduledDate = dayjs(newRefillDate.trim(), 'M/D/YYYY');
      const oneHourFromNow = dayjs().add(1, 'hour');

      // If date is less than 1 hour from now, adjust it
      if (scheduledDate.isBefore(oneHourFromNow)) {
        scheduledDate = oneHourFromNow;
        console.log(
          chalk.yellow(
            `⚠️ Date was adjusted to ${scheduledDate.format('YYYY-MM-DD HH:mm:ss')} UTC (minimum 1 hour from now)`,
          ),
        );
      }

      const finalDate = scheduledDate.toISOString();

      // Get the treatment actor and trigger the moveRefillDate event
      const actor = await this.treatmentService.getActor(treatmentId, (e) =>
        this.logger.verbose(`[RescheduleRefill] ${treatmentId}:`, e),
      );

      if (
        !actor.getSnapshot().can({ type: 'moveRefillDate', date: finalDate })
      ) {
        throw new Error(
          'Treatment refill date cannot be moved in current state',
        );
      }

      actor.send({ type: 'moveRefillDate', date: finalDate });
      await this.treatmentService.updateTreatmentRecord(actor);

      console.log(
        chalk.green(
          `✅ Successfully rescheduled refill to ${scheduledDate.format('YYYY-MM-DD HH:mm:ss')} UTC`,
        ),
      );

      return finalDate;
    } catch (error) {
      console.error(chalk.red('Failed to reschedule refill:'), error.message);
      throw error;
    }
  }

  private showNonInteractiveCommand(
    patientIdentifier: { type: 'email' | 'id'; value: string },
    treatmentId: string,
    newRefillDate?: string | null,
  ): void {
    console.log('\n' + chalk.cyan('=== NON-INTERACTIVE COMMAND ==='));
    console.log(chalk.gray('To run this command non-interactively, use:'));
    console.log('');

    const patientFlag =
      patientIdentifier.type === 'email'
        ? `--email "${patientIdentifier.value}"`
        : `--patient-id "${patientIdentifier.value}"`;

    let command = `pnpm -F api cli trigger-prescribe-failed ${patientFlag} --treatment-id "${treatmentId}"`;

    if (newRefillDate) {
      command += ` --reschedule-date "${newRefillDate}"`;
      console.log(
        chalk.yellow(
          'Note: --reschedule-date flag is not yet implemented for non-interactive mode',
        ),
      );
    }

    console.log(chalk.white(command));
    console.log('');
  }

  private async showAllTreatmentsConfirmation(
    patient: any,
    treatments: TreatmentOption[],
  ): Promise<boolean> {
    console.log('\n' + chalk.cyan('=== CONFIRMATION ==='));
    console.log(
      `${chalk.bold('Patient:')} ${patient.user.firstName} ${patient.user.lastName}`,
    );
    console.log(`${chalk.bold('Email:')} ${patient.user.email}`);
    console.log(`${chalk.bold('Patient ID:')} ${patient.id}`);
    console.log(
      `${chalk.bold('Treatments:')} ${treatments.length} stuck treatments`,
    );

    console.log('\n' + chalk.yellow('Treatment Details:'));
    treatments.forEach((treatment, index) => {
      const currentState = treatment.state?.value
        ? this.extractStatePath(treatment.state.value)
        : treatment.status;

      console.log(
        `  ${index + 1}. ${treatment.productName} (Refill ${treatment.currentRefill}/${treatment.refills}) - ${currentState}`,
      );
    });

    console.log(
      '\n' +
        chalk.yellow(
          'This will mark ALL treatments as prescribed (after prescribe failure) and move them to waitingBetweenRefills state.',
        ),
    );
    console.log('\n');

    return await confirm({
      message: `Process all ${treatments.length} treatments?`,
      default: false,
    });
  }

  private async getNewRefillDate(): Promise<string | null> {
    const newRefillDate = await input({
      message: 'Enter new refill date for all treatments (M/D/YYYY):',
      validate: (input: string) => {
        if (!input.trim()) return true; // Allow empty for no change

        const parsed = dayjs(input, 'M/D/YYYY', true);
        if (!parsed.isValid()) {
          return 'Please enter a valid date in M/D/YYYY format';
        }

        return true;
      },
    });

    return newRefillDate.trim() || null;
  }
}
