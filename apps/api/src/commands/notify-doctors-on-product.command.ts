import confirm from '@inquirer/confirm';
import input from '@inquirer/input';
import search from '@inquirer/search';
import { PrismaService } from '@modules/prisma/prisma.service';
import {
  ConversationToNotify,
  NotifyDoctorsUseCase,
} from '@modules/product/use-cases/notify-doctors.use-case';
import { Injectable, Logger } from '@nestjs/common';
import * as chalk from 'chalk';
import { Command, CommandRunner, Option } from 'nest-commander';

interface ProductOption {
  id: string;
  name: string;
  form: string;
  pharmacyName: string;
  active: boolean | null;
  displayName: string;
}

@Injectable()
@Command({
  name: 'notify-doctors-on-product',
  description:
    'Send doctor notes to all conversations for patients using a specific product',
})
export class NotifyDoctorsOnProductCommand extends CommandRunner {
  private readonly logger = new Logger(NotifyDoctorsOnProductCommand.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly notifyDoctorsUseCase: NotifyDoctorsUseCase,
  ) {
    super();
  }

  @Option({
    flags: '--product-id [productId]',
    description:
      'Product ID to send notifications for (skips product selection)',
  })
  parseProductId(val: string): string {
    return val;
  }

  @Option({
    flags: '--note [note]',
    description: 'Doctor note text to send (skips note input)',
  })
  parseNote(val: string): string {
    return val;
  }

  @Option({
    flags: '--batch-size [batchSize]',
    description: 'Number of conversations to process per batch (default: 100)',
  })
  parseBatchSize(val: string): number {
    const size = parseInt(val, 10);
    if (isNaN(size) || size < 1) {
      throw new Error('Batch size must be a positive number');
    }
    return size;
  }

  @Option({
    flags: '--force',
    description:
      'Skip duplicate detection and send doctor notes even if already sent',
  })
  parseForce(): boolean {
    return true;
  }

  async run(
    _: string[],
    options?: {
      productId?: string;
      note?: string;
      batchSize?: number;
      force?: boolean;
    },
  ): Promise<void> {
    try {
      const isInteractive = !options?.productId || !options?.note;

      // 1. Get product selection
      const selectedProduct = options?.productId
        ? await this.getProductById(options.productId)
        : await this.selectProduct();

      // 2. Get doctor note text
      const doctorNote = options?.note || (await this.getDoctorNote());

      // 3. Get batch size and force flag
      const batchSize = options?.batchSize || 100;
      const force = options?.force || false;

      // 4. Calculate affected conversations and doctors
      const { conversations: conversationsToNotify, skippedDuplicates } =
        await this.notifyDoctorsUseCase.getConversationsToNotify(
          selectedProduct.id,
          doctorNote,
          !force, // skipDuplicates = !force
        );

      const conversationCount = conversationsToNotify.length;
      const doctorCount = new Set(
        conversationsToNotify.map((c) => c.doctorUserId),
      ).size;

      // 5. Show confirmation (skip if both flags provided)
      const needsConfirmation = !options?.productId || !options?.note;
      if (needsConfirmation) {
        const confirmed = await this.showConfirmation(
          selectedProduct,
          doctorNote,
          conversationCount,
          doctorCount,
          skippedDuplicates,
        );

        if (!confirmed) {
          console.log(chalk.yellow('Operation cancelled.'));
          return;
        }
      }

      // 6. Execute optimized notification in batches
      await this.executeBatchedNotification(
        conversationsToNotify,
        doctorNote,
        batchSize,
      );

      // 7. Show summary and non-interactive command if run interactively
      if (isInteractive) {
        console.log(
          chalk.green(
            `\n✅ Successfully sent ${conversationCount} doctor notes to ${doctorCount} doctors`,
          ),
        );
        if (skippedDuplicates > 0) {
          console.log(
            chalk.yellow(
              `⚠️ Skipped ${skippedDuplicates} conversations with duplicate notes`,
            ),
          );
        }
        this.showNonInteractiveCommand(
          selectedProduct.id,
          doctorNote,
          batchSize,
          force,
        );
      }
    } catch (error) {
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  }

  private async selectProduct(): Promise<ProductOption> {
    console.log(chalk.blue('Loading products...'));

    const products = await this.prismaService.product.findMany({
      include: {
        pharmacy: {
          select: { name: true },
        },
      },
      orderBy: [
        { active: 'desc' },
        { pharmacy: { name: 'asc' } },
        { name: 'asc' },
      ],
    });

    if (products.length === 0) {
      throw new Error('No products found in the database');
    }

    const productOptions: ProductOption[] = products.map((product) => ({
      id: product.id,
      name: product.name,
      form: product.form,
      pharmacyName: product.pharmacy.name,
      active: product.active,
      displayName: `${product.pharmacy.name} | ${product.form} | ${product.name} ${product.active ? '(Active)' : '(Inactive)'}`,
    }));

    const selectedProductId = await search({
      message: 'Select a product (type to search):',
      source: async (input: string) => {
        const filtered = productOptions.filter((option) =>
          option.displayName.toLowerCase().includes(input?.toLowerCase() || ''),
        );
        return filtered.map((option) => ({
          name: option.displayName,
          value: option.id,
        }));
      },
    });

    const selectedProduct = productOptions.find(
      (p) => p.id === selectedProductId,
    );
    if (!selectedProduct) {
      throw new Error('Selected product not found');
    }

    return selectedProduct;
  }

  private async getDoctorNote(): Promise<string> {
    const doctorNote = await input({
      message: 'Enter the doctor note text:',
      validate: (input: string) => {
        if (!input?.trim()) {
          return 'Doctor note text is required';
        }
        return true;
      },
    });

    return doctorNote.trim();
  }

  private async showConfirmation(
    product: ProductOption,
    doctorNote: string,
    conversationCount: number,
    doctorCount: number,
    skippedDuplicates: number = 0,
  ): Promise<boolean> {
    console.log('\n' + chalk.cyan('=== CONFIRMATION ==='));
    console.log(`${chalk.bold('Product:')} ${product.displayName}`);
    console.log(`${chalk.bold('Doctor Note:')} ${doctorNote}`);
    console.log(
      `${chalk.bold('Affected Conversations:')} ${conversationCount}`,
    );
    console.log(`${chalk.bold('Unique Doctors:')} ${doctorCount}`);

    if (skippedDuplicates > 0) {
      console.log(`${chalk.bold('Skipped Duplicates:')} ${skippedDuplicates}`);
    }

    if (conversationCount === 0) {
      console.log(
        chalk.yellow('⚠️  No active conversations found for this product.'),
      );
      return false;
    }

    console.log('\n');

    return await confirm({
      message: `Send doctor note to ${conversationCount} conversations affecting ${doctorCount} doctors?`,
      default: false,
    });
  }

  private async executeBatchedNotification(
    conversations: ConversationToNotify[],
    doctorNote: string,
    batchSize: number,
  ): Promise<void> {
    if (conversations.length === 0) {
      console.log(chalk.yellow('No conversations to notify.'));
      return;
    }

    const totalBatches = Math.ceil(conversations.length / batchSize);
    console.log(
      chalk.blue(
        `Sending doctor notes in ${totalBatches} batches of ${batchSize}...`,
      ),
    );

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const startIdx = batchIndex * batchSize;
      const endIdx = Math.min(startIdx + batchSize, conversations.length);
      const batch = conversations.slice(startIdx, endIdx);

      console.log(
        chalk.gray(
          `Processing batch ${batchIndex + 1}/${totalBatches} (${startIdx + 1}-${endIdx}/${conversations.length})...`,
        ),
      );

      await this.notifyDoctorsUseCase.sendDoctorNotes(batch, doctorNote);

      console.log(
        chalk.green(
          `✅ Batch ${batchIndex + 1}/${totalBatches} completed (${batch.length} messages)`,
        ),
      );

      // Small delay between batches to avoid overwhelming the database
      if (batchIndex < totalBatches - 1) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }
  }

  private async getProductById(productId: string): Promise<ProductOption> {
    const product = await this.prismaService.product.findUnique({
      where: { id: productId },
      include: {
        pharmacy: {
          select: { name: true },
        },
      },
    });

    if (!product) {
      throw new Error(`Product with ID ${productId} not found`);
    }

    return {
      id: product.id,
      name: product.name,
      form: product.form,
      pharmacyName: product.pharmacy.name,
      active: product.active,
      displayName: `${product.pharmacy.name} | ${product.form} | ${product.name} ${product.active ? '(Active)' : '(Inactive)'}`,
    };
  }

  private showNonInteractiveCommand(
    productId: string,
    doctorNote: string,
    batchSize?: number,
    force?: boolean,
  ): void {
    console.log('\n' + chalk.cyan('=== NON-INTERACTIVE COMMAND ==='));
    console.log(chalk.gray('To run this command non-interactively, use:'));
    console.log('');

    let command = `pnpm -F api cli notify-doctors-on-product --product-id "${productId}" --note "${doctorNote.replace(/"/g, '\\"')}"`;

    if (batchSize && batchSize !== 100) {
      command += ` --batch-size ${batchSize}`;
    }

    if (force) {
      command += ' --force';
    }

    console.log(chalk.white(command));
    console.log('');
  }
}
