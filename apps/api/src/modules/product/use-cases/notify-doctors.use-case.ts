import { runInDbTransaction } from '@/helpers/transaction';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConversationType, Prisma } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

import type {
  NotifyDoctorsByIdsDto,
  NotifyDoctorsByProductDto,
  NotifyDoctorsByStateDto,
  NotifyDoctorsDto,
} from '../dto/notify-doctors.dto';

export interface ConversationToNotify {
  conversationId: string;
  doctorUserId: string;
  patientId: string;
}

@Injectable()
export class NotifyDoctorsUseCase {
  private readonly logger = new Logger(NotifyDoctorsUseCase.name);

  constructor(private readonly prismaService: PrismaService) {}

  async getConversationsToNotifyByFilter(
    dto: NotifyDoctorsDto,
    noteContent: string,
    skipDuplicates: boolean = true,
  ): Promise<{
    conversations: ConversationToNotify[];
    skippedDuplicates: number;
  }> {
    switch (dto.filterType) {
      case 'product':
        return this.getConversationsByProduct(
          (dto as NotifyDoctorsByProductDto).productId,
          noteContent,
          skipDuplicates,
        );
      case 'state':
        return this.getConversationsByState(
          (dto as NotifyDoctorsByStateDto).stateIds,
          noteContent,
          skipDuplicates,
        );
      case 'doctorIds':
        return this.getConversationsByDoctorIds(
          (dto as NotifyDoctorsByIdsDto).doctorIds,
          noteContent,
          skipDuplicates,
        );
      default:
        throw new Error(`Unsupported filter type: ${(dto as any).filterType}`);
    }
  }

  // Legacy method for backward compatibility
  async getConversationsToNotify(
    productId: string,
    noteContent: string,
    skipDuplicates: boolean = true,
  ): Promise<{
    conversations: ConversationToNotify[];
    skippedDuplicates: number;
  }> {
    return this.getConversationsByProduct(
      productId,
      noteContent,
      skipDuplicates,
    );
  }

  private async getConversationsByProduct(
    productId: string,
    noteContent: string,
    skipDuplicates: boolean = true,
  ): Promise<{
    conversations: ConversationToNotify[];
    skippedDuplicates: number;
  }> {
    this.logger.log(`Getting conversations to notify for product ${productId}`);

    // 1. Validate product exists and get all product price IDs
    const product = await this.prismaService.product.findUnique({
      where: { id: productId },
      include: {
        productPrice: {
          select: { id: true },
        },
      },
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${productId} not found`);
    }

    const productPriceIds = product.productPrice.map((pp) => pp.id);
    if (productPriceIds.length === 0) {
      this.logger.warn(`Product ${productId} has no product prices`);
      return { conversations: [], skippedDuplicates: 0 };
    }

    // 2. Get all active treatments for this product
    const activeStates = ['scheduled', 'inProgress', 'paused', 'failed'];

    const treatments = await this.prismaService.treatment.findMany({
      where: {
        initialProductPriceId: {
          in: productPriceIds,
        },
        status: {
          in: activeStates,
        },
        AND: [
          {
            OR: [
              { state: { path: ['cancelledAt'], equals: null } },
              { state: { path: ['cancelledAt'], equals: Prisma.DbNull } },
            ],
          },
          {
            OR: [
              { state: { path: ['completedAt'], equals: null } },
              { state: { path: ['completedAt'], equals: Prisma.DbNull } },
            ],
          },
          {
            OR: [
              { state: { path: ['uncollectibleAt'], equals: null } },
              { state: { path: ['uncollectibleAt'], equals: Prisma.DbNull } },
            ],
          },
          {
            OR: [
              { state: { path: ['transferredTo'], equals: null } },
              { state: { path: ['transferredTo'], equals: Prisma.DbNull } },
            ],
          },
        ],
      },
      include: {
        patient: {
          include: {
            user: {
              select: {
                id: true,
                deletedAt: true,
              },
            },
          },
        },
      },
    });

    if (treatments.length === 0) {
      this.logger.log(`No active treatments found for product ${productId}`);
      return { conversations: [], skippedDuplicates: 0 };
    }

    // 3. Get valid patient IDs (filter out self-deleted patients)
    const validPatientIds = Array.from(
      new Set(
        treatments
          .filter((treatment) => treatment.patient?.user?.deletedAt === null)
          .map((treatment) => treatment.patientId),
      ),
    );

    if (validPatientIds.length === 0) {
      this.logger.log(
        `No valid patients found for product ${productId} treatments`,
      );
      return { conversations: [], skippedDuplicates: 0 };
    }

    return this.getConversationsForPatients(
      validPatientIds,
      noteContent,
      skipDuplicates,
    );
  }

  private async getConversationsByState(
    stateIds: string[],
    noteContent: string,
    skipDuplicates: boolean = true,
  ): Promise<{
    conversations: ConversationToNotify[];
    skippedDuplicates: number;
  }> {
    this.logger.log(
      `Getting conversations to notify for states: ${stateIds.join(', ')}`,
    );

    // 1. Get doctors who can prescribe in the specified states
    const doctorsInStates = await this.prismaService.doctorsOnState.findMany({
      where: {
        stateId: {
          in: stateIds,
        },
      },
      include: {
        doctor: {
          include: {
            patients: {
              where: {
                user: {
                  deletedAt: null,
                },
              },
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    if (doctorsInStates.length === 0) {
      this.logger.log(`No doctors found for states: ${stateIds.join(', ')}`);
      return { conversations: [], skippedDuplicates: 0 };
    }

    // 2. Get all valid patient IDs from these doctors
    const validPatientIds = Array.from(
      new Set(
        doctorsInStates.flatMap((doctorState) =>
          doctorState.doctor.patients.map((patient) => patient.id),
        ),
      ),
    );

    if (validPatientIds.length === 0) {
      this.logger.log(
        `No valid patients found for doctors in states: ${stateIds.join(', ')}`,
      );
      return { conversations: [], skippedDuplicates: 0 };
    }

    return this.getConversationsForPatients(
      validPatientIds,
      noteContent,
      skipDuplicates,
    );
  }

  private async getConversationsByDoctorIds(
    doctorIds: string[],
    noteContent: string,
    skipDuplicates: boolean = true,
  ): Promise<{
    conversations: ConversationToNotify[];
    skippedDuplicates: number;
  }> {
    this.logger.log(
      `Getting conversations to notify for doctors: ${doctorIds.join(', ')}`,
    );

    // 1. Get patients for the specified doctors
    const patients = await this.prismaService.patient.findMany({
      where: {
        doctorId: {
          in: doctorIds,
        },
        user: {
          deletedAt: null,
        },
      },
      select: {
        id: true,
      },
    });

    if (patients.length === 0) {
      this.logger.log(
        `No valid patients found for doctors: ${doctorIds.join(', ')}`,
      );
      return { conversations: [], skippedDuplicates: 0 };
    }

    const validPatientIds = patients.map((patient) => patient.id);

    return this.getConversationsForPatients(
      validPatientIds,
      noteContent,
      skipDuplicates,
    );
  }

  private async getConversationsForPatients(
    patientIds: string[],
    noteContent: string,
    skipDuplicates: boolean = true,
  ): Promise<{
    conversations: ConversationToNotify[];
    skippedDuplicates: number;
  }> {
    // Get active conversations with duplicate detection
    const conversations = await this.prismaService.conversation.findMany({
      where: {
        patientId: {
          in: patientIds,
        },
        type: ConversationType.patientDoctor,
        status: 'active',
      },
      include: {
        patient: {
          include: {
            doctor: {
              include: {
                user: {
                  select: {
                    id: true,
                  },
                },
              },
            },
          },
        },
        ...(skipDuplicates
          ? {
              messages: {
                where: {
                  type: 'doctorNote',
                },
                orderBy: {
                  createdAt: 'desc' as const,
                },
                take: 1,
                select: {
                  content: true,
                },
              },
            }
          : {}),
      },
    });

    if (conversations.length === 0) {
      this.logger.log(`No active conversations found for specified patients`);
      return { conversations: [], skippedDuplicates: 0 };
    }

    // Process conversations and check for duplicates
    const conversationsToNotify: ConversationToNotify[] = [];
    let skippedDuplicates = 0;

    for (const conversation of conversations) {
      // Validate required relationships
      if (!conversation.patient) {
        this.logger.warn(`No patient for conversation ${conversation.id}`);
        continue;
      }
      if (!conversation.patient.doctor) {
        this.logger.warn(`No doctor for patient ${conversation.patient.id}`);
        continue;
      }
      if (!conversation.patient.doctor.user) {
        this.logger.warn(
          `No user for doctor ${conversation.patient.doctor.id}`,
        );
        continue;
      }

      // Check for duplicate doctor notes if enabled
      if (
        skipDuplicates &&
        'messages' in conversation &&
        conversation.messages &&
        conversation.messages.length > 0
      ) {
        const lastDoctorNote = conversation.messages[0];
        if (lastDoctorNote.content.trim() === noteContent.trim()) {
          skippedDuplicates++;
          continue;
        }
      }

      conversationsToNotify.push({
        conversationId: conversation.id,
        doctorUserId: conversation.patient.doctor.user.id,
        patientId: conversation.patientId,
      });
    }

    this.logger.log(
      `Found ${conversationsToNotify.length} conversations to notify, skipped ${skippedDuplicates} duplicates`,
    );

    return { conversations: conversationsToNotify, skippedDuplicates };
  }

  async sendDoctorNotes(
    conversations: ConversationToNotify[],
    noteContent: string,
  ): Promise<void> {
    if (conversations.length === 0) {
      this.logger.log('No conversations to send doctor notes to');
      return;
    }

    this.logger.log(
      `Sending doctor notes to ${conversations.length} conversations`,
    );

    return runInDbTransaction(this.prismaService, async (prisma) => {
      // Generate message IDs upfront
      const messagesData = conversations.map((conversation) => ({
        id: uuidv4(),
        conversationId: conversation.conversationId,
        userId: conversation.doctorUserId,
        type: 'doctorNote' as const,
        contentType: 'text' as const,
        content: noteContent,
        createdAt: new Date(),
      }));

      // Bulk insert conversation messages
      await prisma.conversationMessage.createMany({
        data: messagesData,
        skipDuplicates: true,
      });

      // Bulk update conversation watchers for doctors
      const doctorUserIds = conversations.map((c) => c.doctorUserId);
      await prisma.conversationWatcher.updateMany({
        where: {
          conversationId: {
            in: conversations.map((c) => c.conversationId),
          },
          user: { type: 'doctor' },
          userId: { in: doctorUserIds },
        },
        data: {
          unreadMessages: { increment: 1 },
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Successfully sent ${conversations.length} doctor notes`);
    });
  }

  async execute(dto: NotifyDoctorsDto): Promise<void> {
    this.logger.log(
      `Executing doctor notes with filter type: ${dto.filterType}`,
    );

    // Get conversations to notify (with duplicate detection enabled)
    const { conversations, skippedDuplicates } =
      await this.getConversationsToNotifyByFilter(dto, dto.message, true);

    if (conversations.length === 0) {
      this.logger.log(
        `No conversations to notify for filter type: ${dto.filterType}`,
      );
      return;
    }

    // Send doctor notes to all conversations
    await this.sendDoctorNotes(conversations, dto.message);

    // Log completion
    this.logger.log(
      `Completed sending doctor notes with ${dto.filterType} filter to ${conversations.length} conversations` +
        (skippedDuplicates > 0
          ? ` (skipped ${skippedDuplicates} duplicates)`
          : ''),
    );
  }

  // Legacy method for backward compatibility
  async executeByProduct(productId: string, message: string): Promise<void> {
    return this.execute({
      filterType: 'product',
      productId,
      message,
    } as NotifyDoctorsByProductDto);
  }
}
