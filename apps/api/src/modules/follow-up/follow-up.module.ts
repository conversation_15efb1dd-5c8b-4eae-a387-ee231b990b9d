import { DoctorPersistence } from '@/adapters/persistence/database/doctor.persistence';
import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { PatientPersistence } from '@/adapters/persistence/database/patient.persistence';
import { QuestionnairePersistence } from '@/adapters/persistence/database/questionnaire.persistence';
import { PrismaModule } from '@/modules/prisma/prisma.module';
import { AiModule } from '@modules/ai/ai.module';
import { AuditLogModule } from '@modules/audit-log/audit-log.module';
import { AuthModule } from '@modules/auth/auth.module';
import { AppCacheModule } from '@modules/cache/cache.module';
import { ContextModule } from '@modules/context/context.module';
import { DoctorModule } from '@modules/doctor/doctor.module';
import { DosespotModule } from '@modules/dosespot/dosespot.module';
import { FollowUpUseCases } from '@modules/follow-up/use-cases';
import { IntercomModule } from '@modules/intercom/intercom.module';
import { PatientModule } from '@modules/patient/patient.module';
import { SegmentModule } from '@modules/segment/segment.module';
import { SnsModule } from '@modules/shared/aws/sns/sns.module';
import { LoggerModule } from '@modules/shared/logger/logger.module';
import { OrchestrationModule } from '@modules/shared/orchestration/orchestration.module';
import { OutboxerModule } from '@modules/shared/outboxer/outboxer.module';
import { SharedModule } from '@modules/shared/shared.module';
import { StripeModule } from '@modules/stripe/stripe.module';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { forwardRef, Module } from '@nestjs/common';

import { FollowUpTreatmentConsumer } from './consumers/follow-up-treatment.consumer';
import { FollowUpConsumer } from './consumers/follow-up.consumer';
import { FollowUpController } from './follow-up.controller';
import { FollowUpWorker } from './follow-up.worker';
import { SendFollowUpNotificationsJob } from './jobs/send-follow-up-notifications.job';
import { FollowUpStateService } from './services/follow-up-state.service';
import { FollowUpService } from './services/follow-up.service';
import { CreatePatientFollowUpUseCase } from './use-cases/create-patient-follow-up.use-case';

@Module({
  imports: [
    LoggerModule,
    IntercomModule,
    PrismaModule,
    forwardRef(() => AuthModule),
    forwardRef(() => SharedModule),
    DosespotModule,
    PatientModule,
    DoctorModule,
    StripeModule,
    AiModule,
    AuditLogModule,
    AppCacheModule,
    OutboxerModule,
    SnsModule,
    ContextModule,
    OrchestrationModule,
    SegmentModule,
  ],
  providers: [
    FollowUpWorker,
    TreatmentService,
    FollowUpService,
    QuestionnairePersistence,
    DoctorPersistence,
    PatientPersistence,
    FollowUpStateService,
    SendFollowUpNotificationsJob,
    PatientPaymentMethodPersistence,
    FollowUpTreatmentConsumer,
    FollowUpConsumer,
    ...FollowUpUseCases,
  ],
  controllers: [FollowUpController],
  exports: [FollowUpService, CreatePatientFollowUpUseCase],
})
export class FollowUpModule {}
