import {
  PrismaService,
  PrismaTransactionalClient,
} from '@/modules/prisma/prisma.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { TreatmentMachineContext } from '@/modules/treatment/states/treatment.state';
import { FollowUpService } from '@modules/follow-up/services/follow-up.service';
import { Injectable, Logger } from '@nestjs/common';
import { Treatment } from '@prisma/client';
import { subDays } from 'date-fns';

import { CancelPatientFollowUpUseCase } from './cancel-patient-follow-up.use-case';

@Injectable()
export class CreatePatientFollowUpUseCase {
  private logger: LoggerService;
  constructor(
    private readonly followUpService: FollowUpService,
    private readonly cancelPatientFollowUp: CancelPatientFollowUpUseCase,
    private readonly prisma: PrismaService,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(
      CreatePatientFollowUpUseCase.name,
    );
  }

  async execute(
    treatment: Treatment,
    ctx: { prisma?: PrismaTransactionalClient } = { prisma: this.prisma },
  ) {
    const treatementState = treatment.state as {
      context: TreatmentMachineContext;
    };

    const { context: treatmentContext } = treatementState;

    const { patientId, treatmentId, endOfLastRefillDate } = treatmentContext;

    // 1. check if treatment is core
    const isCore = treatmentContext.isCore;
    if (!isCore) return;

    // 2. get current active follow up for patient, if exists
    const existing = await this.followUpService.getActive(patientId, {
      prisma: ctx.prisma,
    });
    if (existing) {
      await this.cancelPatientFollowUp.execute(existing.id, {
        prisma: ctx.prisma,
      });
    }

    try {
      const scheduledAt = subDays(endOfLastRefillDate, 7);
      await this.followUpService.create(patientId, treatmentId, scheduledAt, {
        prisma: ctx.prisma,
      });
    } catch (e) {
      this.logger.error(
        e,
        { patientId },
        `[handleTreatmentCreateEvent] Error creating follow up for patient ${patientId}`,
      );
    }
  }
}
