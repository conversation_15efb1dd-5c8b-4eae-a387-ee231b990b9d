import { SegmentService } from '@/modules/segment/segment.service';
import { PatientFollowUpProfile } from '@adapters/persistence/database/patient.persistence';
import { Injectable } from '@nestjs/common';

import { HandleFollowUpNpsSurveyEventDto } from '../dto/handle-follow-up-nps-survey-event.dto';

@Injectable()
export class HandleFollowUpNpsSurveyEventUseCase {
  constructor(private readonly segment: SegmentService) {}

  async execute(
    patient: PatientFollowUpProfile,
    data: HandleFollowUpNpsSurveyEventDto,
  ) {
    void this.segment.track(patient.id, 'NPS Survey Response', {
      properties: {
        value: data.rating,
        source: 'Follow Up',
      },
    });
    return true;
  }
}
