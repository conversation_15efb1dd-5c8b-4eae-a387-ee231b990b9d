import { CancelPatientFollowUpUseCase } from '@modules/follow-up/use-cases/cancel-patient-follow-up.use-case';
import { CreatePatientFollowUpUseCase } from '@modules/follow-up/use-cases/create-patient-follow-up.use-case';
import { GetCompletedFollowUpsForDoctorUseCase } from '@modules/follow-up/use-cases/get-completed-follow-ups-for-doctor.use-case';
import { GetCompletedFollowUpsForPatientUseCase } from '@modules/follow-up/use-cases/get-completed-follow-ups-for-patient.use-case';
import { GetFollowUpByIdUseCase } from '@modules/follow-up/use-cases/get-follow-up-by-id.use-case';
import { HandleFollowUpEventUseCase } from '@modules/follow-up/use-cases/handle-follow-up-event.use-case';
import { ListPatientFollowUpsUseCase } from '@modules/follow-up/use-cases/list-patient-follow-ups.use-case';
import { MarkFollowUpAsReviewedUseCase } from '@modules/follow-up/use-cases/mark-follow-up-as-reviewed.use-case';
import { PatientHasScheduledFollowUpUseCase } from '@modules/follow-up/use-cases/patient-has-scheduled-follow-up.use-case';
import { PatientInProgressFollowUpStatusUseCase } from '@modules/follow-up/use-cases/patient-in-progress-follow-up-status.use-case';
import { ReschedulePatientFollowUpUseCase } from '@modules/follow-up/use-cases/reschedule-patient-follow-up.use-case';

import { HandleFollowUpNpsSurveyEventUseCase } from './handle-follow-up-nps-survey-event.use-case';

export const FollowUpUseCases = [
  ReschedulePatientFollowUpUseCase,
  CancelPatientFollowUpUseCase,
  GetFollowUpByIdUseCase,
  PatientInProgressFollowUpStatusUseCase,
  HandleFollowUpEventUseCase,
  HandleFollowUpNpsSurveyEventUseCase,
  ListPatientFollowUpsUseCase,
  PatientHasScheduledFollowUpUseCase,
  GetCompletedFollowUpsForPatientUseCase,
  GetCompletedFollowUpsForDoctorUseCase,
  MarkFollowUpAsReviewedUseCase,
  CreatePatientFollowUpUseCase,
];
