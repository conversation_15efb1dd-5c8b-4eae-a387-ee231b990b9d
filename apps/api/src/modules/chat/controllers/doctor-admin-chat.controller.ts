import { CapabilityGuard } from '@/modules/auth/guards/capability.guard';
import { CreateDoctorAdminConversationUseCase } from '@/modules/chat/use-cases/create-doctor-admin-conversation.use-case';
import { GetDoctorAdminConversationMessagesUseCase } from '@/modules/chat/use-cases/get-doctor-admin-conversation-messages.use-case';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/roles.decorator';
import { roles } from '@modules/auth/types/roles';
import { AdminConversationFilter } from '@modules/chat/services/chat.service';
import { GetAdminConversationsUseCase } from '@modules/chat/use-cases/get-admin-conversations.use-case';
import { ManageAdminConversationAssignmentUseCase } from '@modules/chat/use-cases/manage-admin-conversation-assignment.use-case';
import { MarkAsReadUseCase } from '@modules/chat/use-cases/mark-as-read.use-case';
import { SendMessageUseCase } from '@modules/chat/use-cases/send-message.use-case';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';

import { MessageDto } from '../dto/message.dto';
import { GetDoctorAdminConversationsGroupCounts } from '../use-cases/get-doctor-admin-conversations-group-counts';

@Controller('doctor-admin-chat')
@UseGuards(AuthGuard('jwt'), RolesGuard, CapabilityGuard)
@Roles([roles.Admin, roles.Doctor])
export class DoctorAdminChatController {
  constructor(
    private readonly createDoctorAdminConversationUseCase: CreateDoctorAdminConversationUseCase,
    private readonly getAdminConversationsUseCase: GetAdminConversationsUseCase,
    private readonly manageAdminConversationAssignmentUseCase: ManageAdminConversationAssignmentUseCase,
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly getDoctorAdminConversationMessagesUseCase: GetDoctorAdminConversationMessagesUseCase,
    private readonly getDoctorAdminConversationsGroupCounts: GetDoctorAdminConversationsGroupCounts,
    private readonly markAsReadUseCase: MarkAsReadUseCase,
  ) {}

  @Post('conversations')
  @Roles([roles.Admin, roles.Doctor])
  async createConversation(
    @Body()
    body: { patientId: string },
    @Req() req: Request,
  ) {
    try {
      const role = req.user['role'];
      let adminUserId: string;
      const { patientId } = body;

      if (role === roles.Admin) adminUserId = req.user['userId'];

      const conversationId =
        await this.createDoctorAdminConversationUseCase.execute({
          patientId,
          adminUserId,
        });

      return { conversationId };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'An unexpected error occurred',
      );
    }
  }

  @Get('conversations')
  @Roles([roles.Admin])
  async getAdminConversations(
    @Query('filter') filter: AdminConversationFilter = 'all',
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Req() req: Request,
  ) {
    const adminUserId = req.user['userId'];
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (isNaN(pageNum) || pageNum < 1) {
      throw new BadRequestException('Invalid page number');
    }

    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      throw new BadRequestException(
        'Invalid limit (must be between 1 and 100)',
      );
    }

    const validFilters: AdminConversationFilter[] = [
      'myInbox',
      'mySent',
      'myUnreplied',
      'allSent',
      'allUnreplied',
      'all',
      'unassigned',
      'closed',
    ];
    if (!validFilters.includes(filter)) {
      throw new BadRequestException(
        `Invalid filter. Must be one of: ${validFilters.join(', ')}`,
      );
    }

    return this.getAdminConversationsUseCase.execute({
      adminUserId,
      filter,
      page: pageNum,
      limit: limitNum,
    });
  }

  @Put('conversations/:conversationId/assign')
  @Roles([roles.Admin])
  async assignConversation(
    @Param('conversationId') conversationId: string,
    @Body() body: { adminUserId?: string },
    @Req() req: Request,
  ) {
    const adminUserId = body.adminUserId || req.user['userId'];

    return this.manageAdminConversationAssignmentUseCase.assignConversation({
      conversationId,
      adminUserId,
    });
  }

  @Put('conversations/:conversationId/unassign')
  @Roles([roles.Admin])
  async unassignConversation(@Param('conversationId') conversationId: string) {
    return this.manageAdminConversationAssignmentUseCase.unassignConversation({
      conversationId,
    });
  }

  @Put('conversations/:conversationId/close')
  @Roles([roles.Admin])
  async closeConversation(
    @Param('conversationId') conversationId: string,
    @Req() req: Request,
  ) {
    const adminUserId = req.user['userId'];

    return this.manageAdminConversationAssignmentUseCase.closeConversation({
      conversationId,
      adminUserId,
    });
  }

  @Get('conversations/group-counts')
  @Roles([roles.Admin])
  async getConversationsCounts(@Req() req: Request) {
    const adminUserId = req.user['userId'];

    return this.getDoctorAdminConversationsGroupCounts.execute(adminUserId);
  }

  /**
   * Send a message in a doctorAdmin conversation (admin or doctor only)
   */
  @Post('/:conversationId/send')
  @Roles([roles.Admin, roles.Doctor])
  async sendMessage(
    @Body() message: MessageDto,
    @Param('conversationId') conversationId: string,
    @Req() request: Request,
  ) {
    return await this.sendMessageUseCase.execute({
      content: message.content,
      contentType: message.contentType,
      conversationId,
      userId: request.user['userId'],
      role: request.user['role'],
      needsReply: false,
      type: 'message',
    });
  }

  @Get('conversations/:conversationId')
  @Roles([roles.Admin, roles.Doctor])
  async getDoctorAdminversation(
    @Param('conversationId') conversationId: string,
  ) {
    return await this.getDoctorAdminConversationMessagesUseCase.execute(
      conversationId,
    );
  }

  /**
   * Mark all messages as read for a user in a doctorAdmin conversation
   */
  @Post(':conversationId/read')
  @Roles([roles.Admin, roles.Doctor])
  async markAsRead(
    @Param('conversationId') conversationId: string,
    @Req() request: Request,
  ) {
    try {
      const userId: string = request.user['userId'];
      return await this.markAsReadUseCase.execute(conversationId, userId);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }
}
