import { Injectable } from '@nestjs/common';

import { AdminConversationFilter, ChatService } from '../services/chat.service';

@Injectable()
export class GetAdminConversationsUseCase {
  constructor(private readonly chatService: ChatService) {}

  async execute({
    adminUserId,
    filter,
    page = 1,
    limit = 20,
  }: {
    adminUserId: string;
    filter: AdminConversationFilter;
    page?: number;
    limit?: number;
  }) {
    return this.chatService.getAdminConversations({
      adminUserId,
      filter,
      page,
      limit,
    });
  }
}
