import { onboardingLegacyV1Machine } from '../states/versions/onboarding-legacy-v1.state';
import { onboardingLegacyV2Machine } from '../states/versions/onboarding-legacy-v2.state';
import { onboardingV1Machine } from '../states/versions/onboarding-v1.state';

export function getQuestionnaireQuestionsFromMachine(
  onboardingVersion: string,
) {
  let machine;
  switch (onboardingVersion) {
    case 'v1':
      machine = onboardingV1Machine;
      break;
    case 'legacy-v1':
      machine = onboardingLegacyV1Machine;
      break;
    case 'legacy-v2':
      machine = onboardingLegacyV2Machine;
      break;
    default:
      machine = onboardingV1Machine;
  }

  // Extract questions from the machine's questionnaire states
  const questions: Record<string, string> = {};
  const questionnaireStates = machine.config.states?.questionnaire?.states;

  if (questionnaireStates) {
    Object.entries(questionnaireStates).forEach(([stateName, stateConfig]) => {
      const meta = (stateConfig as any).meta;
      if (meta?.question) {
        // Map state names to their corresponding questionnaire keys
        const questionKey = mapStateNameToQuestionKey(stateName);
        if (questionKey) {
          questions[questionKey] = meta.question;
        }
      }
    });
  }

  return questions;
}

function mapStateNameToQuestionKey(stateName: string): string | null {
  const mapping: Record<string, string> = {
    age: 'birthDate',
    gender: 'gender',
    isPregnant: 'isPregnant',
    usingGLP1: 'usingGLP1',
    haveDiabetes: 'haveDiabetes',
    doctorVisits: 'doctorVisits',
    qualifyingConditions: 'qualifyingConditions',
    height: 'height',
    weight: 'weight',
    desiredWeight: 'desiredWeight',
    objectives: 'objectives',
    haveAllergies: 'hasAllergies',
    selectAllergies: 'allergies',
    medications: 'medications',
    medicalConditions: 'medicalConditions',
    additionalInformation: 'additionalInformation',
  };

  return mapping[stateName] || null;
}
