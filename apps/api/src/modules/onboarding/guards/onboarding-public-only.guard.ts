import type { Request } from 'express';
import { isAuthenticatedCookie } from '@modules/onboarding/types/cookie.types';
import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';

/**
 * Guard that ensures public onboarding endpoints are only accessible
 * when the user is NOT authenticated (no auth tokens in cookie)
 */
@Injectable()
export class OnboardingPublicOnlyGuard implements CanActivate {
  private readonly cookieName = 'willow-onboarding';

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const cookieValue = request.cookies?.[this.cookieName];

    if (!cookieValue) {
      // No cookie, allowed
      return true;
    }

    try {
      const parsedCookie = JSON.parse(cookieValue);

      // Check if cookie has auth tokens
      if (isAuthenticatedCookie(parsedCookie)) {
        throw new ForbiddenException(
          'This endpoint is not accessible when already authenticated. Please continue with the authenticated onboarding flow.',
        );
      }
    } catch (error) {
      // If parsing fails, allow access (cookie might be corrupted)
      if (error instanceof ForbiddenException) {
        throw error;
      }
    }

    return true;
  }
}
