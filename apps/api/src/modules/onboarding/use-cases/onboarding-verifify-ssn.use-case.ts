import { SegmentService } from '@/modules/segment/segment.service';
import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { PhotoIdVerificationDto } from '@modules/onboarding/dto/photo-id-verification.dto';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states/versions';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { VouchedService } from '@modules/shared/services/vouched.service';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { fromPromise, waitFor } from 'xstate';

import { OnboardingEventEmitterService } from './onboarding-event-emitter.service';

@Injectable()
export class OnboardingVerifySSNUseCase {
  private readonly logger = new Logger(OnboardingVerifySSNUseCase.name);

  constructor(
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPersistence: PatientPersistence,
    private readonly onboardingEventEmitter: OnboardingEventEmitterService,
    private readonly vouchedService: VouchedService,
    private readonly segment: SegmentService,
  ) {}

  async execute(
    profile: PatientOnboardingProfile,
    dto: PhotoIdVerificationDto,
  ) {
    if (profile.status !== 'onboardingPending') {
      throw new Error('Invalid onboarding state');
    }

    // Create the verifySsn actor
    const actors = {
      verifySsn: fromPromise<
        {
          success: boolean;
          verificationId?: string;
        },
        {
          lastFourSSN: string;
          firstName: string;
          lastName: string;
          dateOfBirth: string;
          phone: string;
        }
      >(async ({ input }) => {
        const verificationData = {
          firstName: input.firstName,
          lastName: input.lastName,
          phone: input.phone,
          dateOfBirth: input.dateOfBirth,
          ssn: input.lastFourSSN,
        };

        const vouchedResponse =
          await this.vouchedService.verifyIdentity(verificationData);

        // If SSN verification failed, throw an error to trigger onError transition
        if (!vouchedResponse.ssnMatch) {
          await this.segment.track(profile.id, 'VerificationServiceReject', {
            properties: { type: 'non-document' },
          });
          throw new Error('SSN verification failed');
        }

        void this.segment.track(profile.id, 'VerificationServiceAccept', {
          properties: { type: 'non-document' },
        });

        // Return success data for onDone transition
        return {
          success: true,
          verificationId: vouchedResponse.verificationId,
        };
      }),
    };

    const onboarding = await this.onboardingService.getCurrentOnboardingActor(
      profile.onboardingVersion as OnboardingVersion,
      profile.onboardingState as AnyOnboardingSnapshot,
      actors,
    );

    // Validate we're in the correct state
    const currentState = onboarding.getSnapshot();
    if (!this.isInSsnCheckState(currentState)) {
      throw new Error('SSN verification not allowed in current state');
    }

    // Create the submit event for SSN verification
    const event = {
      type: 'submit',
      subtype: 'ssnCheck',
      value: {
        lastFourSSN: dto.lastFourSSN,
      },
    };

    void this.segment.track(profile.id, 'VerificationServiceSubmit', {
      properties: { type: 'non-document' },
    });

    // Start the actor and send the event
    onboarding.start();
    onboarding.send(event);

    // Wait for the verifySsn invocation to complete
    const resolvedSnapshot = await this.waitForStateResolution(onboarding);

    // Persist the resolved state
    await this.patientPersistence.updateOnboarding(
      profile.id,
      resolvedSnapshot,
    );
    await this.onboardingEventEmitter.execute(profile, resolvedSnapshot);

    return this.onboardingService.getCurrentOnboardingState(
      profile.onboardingVersion as OnboardingVersion,
      resolvedSnapshot,
    );
  }

  private isInSsnCheckState(snapshot: any): boolean {
    const stateValue = snapshot.value;
    if (typeof stateValue === 'string') {
      return stateValue === 'ssnCheck';
    }
    // Handle nested state objects
    return JSON.stringify(stateValue).includes('ssnCheck');
  }

  private getStateString(value: any): string {
    if (typeof value === 'string') {
      return value;
    }
    if (typeof value === 'object' && value !== null) {
      const keys = Object.keys(value as Record<string, any>);
      if (keys.length === 1) {
        const key = keys[0];
        const nestedValue = value[key];
        if (typeof nestedValue === 'object') {
          return `${key}.${this.getStateString(nestedValue)}`;
        }
        return `${key}.${nestedValue}`;
      }
    }
    return String(value);
  }

  private async waitForStateResolution(actor: any) {
    try {
      return await waitFor(
        actor,
        (state) => {
          const currentStateValue = this.getStateString(state.value);
          // Wait until we're no longer in the verifySsn invoking state
          return !currentStateValue.includes('verifySsn');
        },
        { timeout: 30000 },
      );
    } catch (error) {
      this.logger.error(
        'Error waiting for SSN verification state resolution:',
        error,
      );
      return actor.getSnapshot();
    }
  }
}
