import { Injectable } from '@nestjs/common';

import { OnboardingCookieService } from '../services/onboarding-cookie.service';

@Injectable()
export class OnboardingClearCookieUseCase {
  constructor(private readonly cookieService: OnboardingCookieService) {}

  execute() {
    // Clear the onboarding cookie
    this.cookieService.clearCookie();

    return {
      success: true,
      message: 'Onboarding cookie cleared',
    };
  }
}
