import { Injectable, Logger } from '@nestjs/common';

import type { OnboardingEvents } from '../states/versions/types/onboarding-generic.types';
import {
  OnboardingPreSignupBaseUseCase,
  PreSignupResponse,
} from './onboarding-pre-signup-base.use-case';

@Injectable()
export class OnboardingPreSignupBackUseCase extends OnboardingPreSignupBaseUseCase {
  protected readonly logger = new Logger(OnboardingPreSignupBackUseCase.name);

  async execute(): Promise<PreSignupResponse> {
    const event: OnboardingEvents = {
      type: 'back',
    };

    return this.executeBase(event);
  }
}
