import { ContextService } from '@/modules/context/context.service';
import { PatientAddToWaitingListUseCase } from '@/modules/patient/use-cases/patient-add-to-waiting-list-use.case';
import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import { fromPromise } from 'xstate';

import type { OnboardingEvents } from '../states/versions/types/onboarding-generic.types';
import { PreSignupWaitingListDto } from '../dto/pre-signup/waiting-list.dto';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';
import { OnboardingStateService } from '../services/onboarding-state.service';
import {
  OnboardingPreSignupBaseUseCase,
  PreSignupResponse,
} from './onboarding-pre-signup-base.use-case';

@Injectable()
export class OnboardingPreSignupWaitingListUseCase extends OnboardingPreSignupBaseUseCase {
  protected readonly logger = new Logger(
    OnboardingPreSignupWaitingListUseCase.name,
  );

  constructor(
    cookieService: OnboardingCookieService,
    onboardingStateService: OnboardingStateService,
    private readonly patientAddToWaitingListUseCase: PatientAddToWaitingListUseCase,
    private readonly contextService: ContextService,
  ) {
    super(cookieService, onboardingStateService);
  }

  async execute(data: PreSignupWaitingListDto): Promise<PreSignupResponse> {
    // Get current context to merge with incoming data
    const cookie = this.cookieService.getCookie();
    if (!cookie?.stateSnapshot) {
      throw new Error('No state snapshot found');
    }

    // Create a temporary actor just to get the context
    const tempActor = this.createActor(cookie);
    tempActor.start();
    const context = tempActor.getSnapshot().context;
    tempActor.stop();

    // Create the submit event for waiting list
    const event: OnboardingEvents = {
      type: 'submit',
      subtype: 'waitingList',
      value: {
        email: data.email || context.email,
        state: context.state || '',
      },
    };

    const response = await this.executeBase(event);

    // Clear the cookie after successful waiting list submission
    // This prevents users from being redirected back to the thank you page
    // when they try to start the onboarding process again
    if (
      'currentState' in response &&
      response.currentState === 'preSignup.unsupportedStateThankYou'
    ) {
      this.cookieService.clearCookie();
    }

    return response;
  }

  protected async validateStateRequirements(
    currentState: string,
    event: OnboardingEvents,
  ): Promise<void> {
    // Waiting list submission should only happen in the unsupportedState
    if (!currentState.includes('unsupportedState')) {
      throw new ForbiddenException(
        `Waiting list submission not allowed in state: ${currentState}`,
      );
    }

    // Validate the event has the required data
    if (
      event.type !== 'submit' ||
      event.subtype !== 'waitingList' ||
      !event.value?.email ||
      !event.value?.state
    ) {
      throw new ForbiddenException('Invalid waiting list submission data');
    }
  }

  protected createActor(cookie: any) {
    const actors = {
      addToWaitingList: fromPromise<
        { success: boolean },
        { email: string; state: string }
      >(async ({ input }) => {
        const { email, state } = input;
        try {
          const headers = this.contextService.getRequestHeaders() || {};
          await this.patientAddToWaitingListUseCase.execute(headers, {
            email,
            state,
          });
          return { success: true };
        } catch (error) {
          console.error('Error adding to waiting list:', error);
          return { success: false };
        }
      }),
    };

    return super.createActor(cookie, actors);
  }
}
