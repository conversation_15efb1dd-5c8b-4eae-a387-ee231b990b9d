import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states/versions';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { Injectable, Logger } from '@nestjs/common';

import { OnboardingEventEmitterService } from './onboarding-event-emitter.service';

@Injectable()
export class OnboardingSkipSsnUseCase {
  private readonly logger = new Logger(OnboardingSkipSsnUseCase.name);

  constructor(
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPersistence: PatientPersistence,
    private readonly onboardingEventEmitter: OnboardingEventEmitterService,
  ) {}

  async execute(profile: PatientOnboardingProfile) {
    if (profile.status !== 'onboardingPending') {
      throw new Error('Invalid onboarding state');
    }

    const onboarding = await this.onboardingService.getCurrentOnboardingActor(
      profile.onboardingVersion as OnboardingVersion,
      profile.onboardingState as AnyOnboardingSnapshot,
    );

    // Validate we're in the correct state
    const currentState = onboarding.getSnapshot();
    if (!this.isInSsnCheckState(currentState)) {
      throw new Error('SSN skip not allowed in current state');
    }

    this.logger.log(`Skipping SSN verification for patient ${profile.id}`);

    // Perform transition and get new state
    const result = this.onboardingService.performTransition(onboarding, 'skip');

    // Persist new state
    const snapshot = result.getSnapshot();
    await this.patientPersistence.updateOnboarding(profile.id, snapshot);
    this.onboardingEventEmitter.execute(profile, snapshot);

    return this.onboardingService.getCurrentOnboardingState(
      profile.onboardingVersion as OnboardingVersion,
      snapshot,
    );
  }

  private isInSsnCheckState(snapshot: any): boolean {
    const stateValue = snapshot.value;
    if (typeof stateValue === 'string') {
      return stateValue === 'ssnCheck';
    }
    // Handle nested state objects
    return JSON.stringify(stateValue).includes('ssnCheck');
  }
}
