import type {
  PostQuestionnaireData,
  PreSignupData,
  QuestionnaireStepData,
} from './types/onboarding-generic.types';

// Pure helper functions for action logic
// These contain the business logic without XState-specific types

export const getRejectPayload = (reason: string) => ({
  rejected: true,
  rejectedReason: reason,
});

export const getClearRejectedPayload = () => ({
  rejected: false,
  rejectedReason: undefined,
});

export const getCompletePayload = () => ({
  questionnaireCompleted: true,
});

export const mergeQuestionnaireData = (
  context: any,
  event: any,
  params?: any,
) => {
  // For 'next' events, merge the event value data
  const data =
    event.type === 'next' && event.value
      ? (event.value as QuestionnaireStepData)
      : {};

  // Always merge params (for radio button selections) and data
  const updatedQuestionnaire = {
    ...context.questionnaire,
    ...data,
    ...params,
  };

  return {
    ...context,
    questionnaire: updatedQuestionnaire,
  };
};

export const mergeStoreData = (context: any, event: any) => {
  if (event.type !== 'next') return context;
  const data = event.value as PostQuestionnaireData;
  return { ...context, ...data };
};

export const mergeClientSecretData = (context: any, event: any) => {
  if (event.type !== 'next' || !event.value) return context;
  const data = event.value as PostQuestionnaireData;
  if (!data.paymentIntent) return context;
  return { ...context, paymentIntent: data.paymentIntent };
};

export const mergePreSignupData = (context: any, event: any) => {
  if (event.type === 'next' && event.value) {
    const data = event.value as PreSignupData;
    return { ...context, ...data };
  }

  if (event.type === 'submit') {
    return { ...context, ...event.value };
  }

  return context;
};

// V2-specific SSN helpers
export const mergeSsnData = (context: any, event: any) => {
  if (event.type === 'submit' && event.subtype === 'ssnCheck') {
    return {
      ...context,
      lastFourSSN: event.value.lastFourSSN,
      vouchedVerifiedAt: new Date(),
    };
  }
  return context;
};

export const getSsnSuccessPayload = (
  context: any,
  output: { success: boolean; verificationId?: string },
) => ({
  ...context,
  ssnVerified: output.success,
  ssnCheckFailed: false,
  ssnVerificationId: output.verificationId,
});

export const getSsnFailurePayload = (context: any) => ({
  ...context,
  ssnVerified: false,
  ssnCheckFailed: true,
  ssnVerificationId: undefined,
});

export const getClearSsnVerificationPayload = (context: any) => ({
  ...context,
  ssnVerified: false,
  ssnCheckFailed: false,
  ssnVerificationId: undefined,
});

// V3-specific BMI calculation helper
export const calculateAndStoreBMI = (context: any, event: any) => {
  if (event.type !== 'next' || !event.value) return context;

  const weight = event.value.weight; // in pounds
  const height = context.questionnaire.height; // in inches

  if (!weight || !height) return context;

  // Convert pounds to kg and inches to cm, then to meters
  const weightInKg = weight / 2.2046;
  const heightInCm = height * 2.54;
  const heightInM = heightInCm / 100;

  // Calculate BMI: weight(kg) / height(m)²
  const bmi = Number((weightInKg / (heightInM * heightInM)).toFixed(2));

  return {
    ...context,
    bmi,
    questionnaire: {
      ...context.questionnaire,
      weight,
    },
  };
};
