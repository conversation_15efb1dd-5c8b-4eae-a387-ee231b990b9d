import * as dayjs from 'dayjs';

import type { QuestionnaireStepData } from './types/onboarding-generic.types';

// Generic guard type that works with any context and event types
type GuardFunction<TContext = any, TEvent = any> = (
  args: { context: TContext; event: TEvent },
  params?: any,
) => boolean;

export const isUnderAge: GuardFunction = ({ event }) => {
  if ((event as any).type !== 'next' || !(event as any).value) return false;
  const data = (event as any).value as QuestionnaireStepData;
  if (!data.birthDate) return false;
  const birthDate = dayjs(data.birthDate);
  const eighteenYearsAgo = dayjs().subtract(18, 'year');

  return birthDate.isAfter(eighteenYearsAgo);
};

export const isOverAge: GuardFunction = ({ event }) => {
  if ((event as any).type !== 'next' || !(event as any).value) return false;
  const data = (event as any).value as QuestionnaireStepData;
  if (!data.birthDate) return false;
  const birthDate = dayjs(data.birthDate);
  const seventyFiveYearsAgo = dayjs().subtract(75, 'year');

  return birthDate.isBefore(seventyFiveYearsAgo);
};

export const hasSsnSuccess: GuardFunction = ({ context }) => {
  return !!(context as any).ssnVerified && !!(context as any).ssnVerificationId;
};

export const hasSsnCheckFailed: GuardFunction = ({ context }) => {
  return (context as any).ssnCheckFailed === true;
};

// V3-specific medical necessity guard
export const needsMedicalNecessity: GuardFunction = ({ context, event }) => {
  if ((event as any).type !== 'next' || !(event as any).value) return false;

  const objectives = (event as any).value.objectives;
  const bmi = (context as any).bmi;

  // Check if patient selected none of the options (only "none" selected or empty array)
  const hasNoObjectives =
    !objectives ||
    objectives.length === 0 ||
    (objectives.length === 1 && objectives[0] === 'none');

  // Check if BMI is <= 27
  const hasHighBMI = bmi && bmi >= 27;

  // Reject if both conditions are true
  return hasNoObjectives && hasHighBMI;
};
