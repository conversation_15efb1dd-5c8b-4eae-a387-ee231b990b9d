import type {
  BaseOnboardingContext,
  OnboardingEvents,
} from './onboarding-generic.types';

// V1 context is equivalent to the base context (no additional fields)
export type OnboardingV1Context = BaseOnboardingContext;

// V1 events are equivalent to modern onboarding events (no additional events)
export type OnboardingV1Events = OnboardingEvents;

// Input type for machine initialization
export interface OnboardingV1Input {
  version: 'v1';
  existingData?: Partial<OnboardingV1Context>;
  statePersistence?: any; // Service to check state availability
}

// Actor input/output types for type-safe invocations
export interface CreateUserInput {
  context: OnboardingV1Context;
}
