import type { ShippingInfoDto } from '@modules/onboarding/dto/shipping-info.dto';
import type { DesiredTreatment } from '@modules/onboarding/use-cases/onboarding-desired-treatment.use-case';

// Generic types that are shared between onboarding versions

// Pre-signup data types
export interface PreSignupData {
  state?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  password?: string;
  getPromotionsSMS?: boolean;
  promoCoupon?: string;
  referralCode?: string;
}

// Base questionnaire step data types (without objectives - used by Legacy V1)
export interface BaseQuestionnaireStepData {
  birthDate?: string;
  gender?: 'male' | 'female';
  height?: number;
  weight?: number;
  desiredWeight?: number;
  usingGLP1?: 'yes' | 'no';
  isPregnant?: 'yes' | 'no';
  haveDiabetes?: 'yes' | 'no';
  doctorVisits?: 'yes' | 'no';
  hasAllergies?: 'yes' | 'no';
  allergies?: string[];
  medications?: string[];
  medicalConditions?: string[];
  qualifyingConditions?: string[];
  additionalInformation?: string;
  priorConditions?: string[];
}

// Extended questionnaire step data types (with objectives - used by Legacy V2, V1, V2)
export interface ExtendedQuestionnaireStepData
  extends BaseQuestionnaireStepData {
  objectives?: string[];
}

// Backward compatibility alias
export type QuestionnaireStepData = ExtendedQuestionnaireStepData;

// Post-questionnaire data types
export interface PostQuestionnaireData {
  productType?: string;
  products?: DesiredTreatment[];
  'id-photo'?: string;
  'face-photo'?: string;
  shippingInfo?: ShippingInfoDto;
  paymentIntent?: string;
}

// Actor input/output types
export interface CheckStateEnabledInput {
  stateCode?: string;
}

export interface CheckStateEnabledOutput {
  stateCode: string;
}

export interface AddToWaitingListInput {
  email: string;
  state: string;
}

export interface AddToWaitingListOutput {
  success: boolean;
}

export interface ValidateNameInput {
  firstName: string;
  lastName: string;
}

export interface ValidateNameOutput {
  valid: boolean;
  errors?: string[];
}

export interface CreateUserOutput {
  success: boolean;
  authData?: any;
}

// Base context type that contains all common fields between onboarding versions
export interface BaseOnboardingContext
  extends PreSignupData,
    PostQuestionnaireData {
  // State management
  rejected?: boolean;
  questionnaireCompleted: boolean;
  rejectedReason?: string;

  // Auth data (populated after user creation)
  authData?: any;

  // Questionnaire data (structured)
  questionnaire: Partial<QuestionnaireStepData>;

  // Additional post-questionnaire data
  pharmacyId?: string;
}

// Legacy base context (used by legacy versions without pre-signup flow)
export interface BaseLegacyOnboardingContext<
  TQuestionnaire = ExtendedQuestionnaireStepData,
> extends PostQuestionnaireData {
  // State management
  rejected?: boolean;
  questionnaireCompleted: boolean;
  rejectedReason?: string;

  // Questionnaire data (structured)
  questionnaire: Partial<TQuestionnaire>;

  // Additional post-questionnaire data
  pharmacyId?: string;
}

// Base event types shared across all onboarding versions

// Navigation events (common to all versions)
export type BaseNavigationEvents =
  | {
      type: 'next';
      value?: PreSignupData | QuestionnaireStepData | PostQuestionnaireData;
    }
  | { type: 'back' };

// Questionnaire-specific answer events (common to all versions)
export type BaseQuestionnaireEvents =
  | { type: 'male' }
  | { type: 'female' }
  | { type: 'yes' }
  | { type: 'no' };

// Submit events with specific payloads (modern versions only)
export type BaseSubmitEvents =
  | {
      type: 'submit';
      subtype: 'createAccount';
      value: {
        email: string;
        password: string;
        phone: string;
        firstName: string;
        lastName: string;
        getPromotionsSMS?: boolean;
        promoCoupon?: string;
        referralCode?: string;
      };
    }
  | {
      type: 'submit';
      subtype: 'waitingList';
      value: {
        email: string;
        state: string;
      };
    }
  | {
      type: 'submit';
      subtype: 'validateName';
      value: {
        firstName: string;
        lastName: string;
      };
    };

// Payment events (legacy versions only)
export type BasePaymentEvents = { type: 'update' } | { type: 'complete' };

// Composed event types for reuse
export type BaseOnboardingEvents =
  | BaseNavigationEvents
  | BaseQuestionnaireEvents;
export type OnboardingEvents = BaseOnboardingEvents | BaseSubmitEvents;
export type LegacyOnboardingEvents = BaseOnboardingEvents | BasePaymentEvents;
