import type {
  BaseOnboardingContext,
  OnboardingEvents,
} from './onboarding-generic.types';

// V3 context extends the base context with SSN verification fields and BMI
export interface OnboardingV3Context extends BaseOnboardingContext {
  // SSN verification data (inherited from V2)
  ssnVerified?: boolean;
  ssnCheckFailed?: boolean;
  ssnVerificationId?: string;
  lastFourSSN?: string;
  vouchedVerifiedAt?: Date;

  // V3-specific: BMI calculation
  bmi?: number;
}

// V3 events extend modern events with SSN-specific submit and skip event
export type OnboardingV3Events =
  | OnboardingEvents
  | {
      type: 'submit';
      subtype: 'ssnCheck';
      value: {
        lastFourSSN: string;
      };
    }
  | { type: 'skip' };

// Input type for machine initialization
export interface OnboardingV3Input {
  version: 'v3';
  existingData?: Partial<OnboardingV3Context>;
  statePersistence?: any; // Service to check state availability
}

// Actor input/output types for type-safe invocations
export interface CreateUserInput {
  context: OnboardingV3Context;
}

export interface VerifySsnInput {
  lastFourSSN: string;
  firstName: string;
  lastName: string;
  phone: string;
}

export interface VerifySsnOutput {
  success: boolean;
  verificationId?: string;
}
