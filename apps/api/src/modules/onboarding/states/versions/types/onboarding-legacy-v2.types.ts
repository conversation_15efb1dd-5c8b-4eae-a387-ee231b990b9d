import type {
  BaseLegacyOnboardingContext,
  ExtendedQuestionnaireStepData,
  LegacyOnboardingEvents,
} from './onboarding-generic.types';

// Legacy V2 context uses extended questionnaire (with objectives)
export type OnboardingLegacyV2Context =
  BaseLegacyOnboardingContext<ExtendedQuestionnaireStepData>;

// Legacy V2 events are equivalent to legacy onboarding events (no additional events)
export type OnboardingLegacyV2Events = LegacyOnboardingEvents;

// Input type for machine initialization
export interface OnboardingLegacyV2Input {
  version: 'legacy-v2';
  existingData?: Partial<OnboardingLegacyV2Context>;
}
