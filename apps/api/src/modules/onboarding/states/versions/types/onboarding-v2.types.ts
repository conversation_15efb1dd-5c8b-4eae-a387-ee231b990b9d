import type {
  BaseOnboardingContext,
  OnboardingEvents,
} from './onboarding-generic.types';

// V2 context extends the base context with SSN verification fields
export interface OnboardingV2Context extends BaseOnboardingContext {
  // SSN verification data (V2-specific)
  ssnVerified?: boolean;
  ssnCheckFailed?: boolean;
  ssnVerificationId?: string;
  lastFourSSN?: string;
  vouchedVerifiedAt?: Date;
}

// V2 events extend modern events with SSN-specific submit and skip event
export type OnboardingV2Events =
  | OnboardingEvents
  | {
      type: 'submit';
      subtype: 'ssnCheck';
      value: {
        lastFourSSN: string;
      };
    }
  | { type: 'skip' };

// Input type for machine initialization
export interface OnboardingV2Input {
  version: 'v2';
  existingData?: Partial<OnboardingV2Context>;
  statePersistence?: any; // Service to check state availability
}

// Actor input/output types for type-safe invocations
export interface CreateUserInput {
  context: OnboardingV2Context;
}

export interface VerifySsnInput {
  lastFourSSN: string;
  firstName: string;
  lastName: string;
  phone: string;
}

export interface VerifySsnOutput {
  success: boolean;
  verificationId?: string;
}
