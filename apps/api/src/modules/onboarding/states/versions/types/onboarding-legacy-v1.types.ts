import type {
  BaseLegacyOnboardingContext,
  BaseQuestionnaireStepData,
  LegacyOnboardingEvents,
} from './onboarding-generic.types';

// Legacy V1 context uses base questionnaire (without objectives)
export type OnboardingLegacyV1Context =
  BaseLegacyOnboardingContext<BaseQuestionnaireStepData>;

// Legacy V1 events are equivalent to legacy onboarding events (no additional events)
export type OnboardingLegacyV1Events = LegacyOnboardingEvents;

// Input type for machine initialization
export interface OnboardingLegacyV1Input {
  version: 'legacy-v1';
  existingData?: Partial<OnboardingLegacyV1Context>;
}
