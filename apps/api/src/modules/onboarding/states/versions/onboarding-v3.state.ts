import { assign, fromPromise, setup } from 'xstate';

import type {
  AddToWaitingListInput,
  AddToWaitingListOutput,
  CheckStateEnabledInput,
  CheckStateEnabledOutput,
  CreateUserOutput,
  ValidateNameInput,
  ValidateNameOutput,
} from './types/onboarding-generic.types';
import type {
  CreateUserInput,
  OnboardingV3Context,
  OnboardingV3Events,
  OnboardingV3Input,
  VerifySsnInput,
  VerifySsnOutput,
} from './types/onboarding-v3.types';
import { QUESTIONNAIRE_QUESTIONS } from '../questionnaire-questions';
import * as actions from './actions';
import * as guards from './guards';

export const onboardingV3Machine = setup({
  types: {
    context: {} as OnboardingV3Context,
    events: {} as OnboardingV3Events,
    input: {} as OnboardingV3Input,
  },
  actors: {
    // This will be provided by the use case when creating the actor
    checkStateEnabled: fromPromise<
      CheckStateEnabledOutput,
      CheckStateEnabledInput
    >(async () => {
      throw new Error('checkStateEnabled actor must be provided at runtime');
    }),
    createUser: fromPromise<CreateUserOutput, CreateUserInput>(async () => {
      throw new Error('createUser actor must be provided at runtime');
    }),
    addToWaitingList: fromPromise<
      AddToWaitingListOutput,
      AddToWaitingListInput
    >(async () => {
      throw new Error('addToWaitingList actor must be provided at runtime');
    }),
    validateName: fromPromise<ValidateNameOutput, ValidateNameInput>(
      async () => {
        throw new Error('validateName actor must be provided at runtime');
      },
    ),
    verifySsn: fromPromise<VerifySsnOutput, VerifySsnInput>(async () => {
      throw new Error('verifySsn actor must be provided at runtime');
    }),
  },
  guards: { ...guards },
  actions: {
    reject: assign((_, params: { reason: string }) =>
      actions.getRejectPayload(params.reason),
    ),
    clearRejected: assign(() => actions.getClearRejectedPayload()),
    complete: assign(() => actions.getCompletePayload()),
    storeQuestionnaire: assign(({ context, event }, params) =>
      actions.mergeQuestionnaireData(context, event, params),
    ),
    store: assign(({ context, event }) =>
      actions.mergeStoreData(context, event),
    ),
    storeClientSecret: assign(({ context, event }) =>
      actions.mergeClientSecretData(context, event),
    ),
    storePreSignup: assign(({ context, event }) =>
      actions.mergePreSignupData(context, event),
    ),
    storeSsnData: assign(({ context, event }) =>
      actions.mergeSsnData(context, event),
    ),
    storeSsnSuccess: assign(
      (
        { context },
        params: { output: { success: boolean; verificationId?: string } },
      ) => actions.getSsnSuccessPayload(context, params.output),
    ),
    storeSsnFailure: assign(({ context }) =>
      actions.getSsnFailurePayload(context),
    ),
    clearSsnVerificationState: assign(({ context }) =>
      actions.getClearSsnVerificationPayload(context),
    ),
    calculateAndStoreBMI: assign(({ context, event }) =>
      actions.calculateAndStoreBMI(context, event),
    ),
  },
}).createMachine({
  context: ({ input }) => ({
    questionnaireCompleted: false,
    questionnaire: {},
    ...(input?.existingData || {}),
  }),
  id: 'onboarding-v3',
  initial: 'preSignup',
  meta: {
    version: 'v3',
    total: 3 + 15 + 12,
  },
  states: {
    preSignup: {
      initial: 'stateSelection',
      states: {
        stateSelection: {
          on: {
            next: {
              target: 'checkingState',
              actions: 'storePreSignup',
            },
          },
          meta: { step: 1, name: 'Account Creation' },
        },
        checkingState: {
          invoke: {
            src: 'checkStateEnabled',
            input: ({ context }) => ({
              stateCode: context.state,
            }),
            onDone: {
              target: 'firstAndLastName',
              actions: assign(({ event }) => ({
                state: event.output.stateCode,
              })),
            },
            onError: {
              target: 'unsupportedState',
            },
          },
        },
        firstAndLastName: {
          on: {
            submit: {
              target: 'validatingName',
              actions: 'storePreSignup',
            },
            back: {
              target: 'stateSelection',
            },
          },
          meta: { step: 2, name: 'Account Creation' },
        },
        validatingName: {
          invoke: {
            src: 'validateName',
            input: ({ context }) => ({
              firstName: context.firstName || '',
              lastName: context.lastName || '',
            }),
            onDone: {
              target: 'createAccount',
            },
            onError: {
              target: 'firstAndLastName',
            },
          },
        },
        createAccount: {
          on: {
            submit: {
              target: 'creatingUser',
              actions: 'storePreSignup',
            },
            back: {
              target: 'firstAndLastName',
            },
          },
          meta: { step: 3, name: 'Account Creation' },
        },
        creatingUser: {
          invoke: {
            src: 'createUser',
            input: ({ context }) => ({ context }),
            onDone: {
              target: '#onboarding-v3.questionnaire',
              actions: assign(({ event }) => {
                const output = event.output as {
                  success: boolean;
                  authData?: any;
                };
                return { authData: output.authData };
              }),
            },
            onError: {
              target: 'createAccount',
            },
          },
        },
        unsupportedState: {
          on: {
            submit: {
              target: 'addingToWaitingList',
            },
            back: {
              target: 'stateSelection',
              actions: 'clearRejected',
            },
          },
          meta: { step: 2, name: 'Account Creation' },
        },
        addingToWaitingList: {
          invoke: {
            src: 'addToWaitingList',
            input: ({ context, event }) => ({
              email:
                event.type === 'submit' && event.subtype === 'waitingList'
                  ? event.value.email
                  : context.email,
              state: context.state,
            }),
            onDone: {
              target: 'unsupportedStateThankYou',
            },
            onError: {
              target: 'unsupportedState',
            },
          },
        },
        unsupportedStateThankYou: {
          on: {
            back: {
              target: 'stateSelection',
            },
          },
          meta: { step: 3, name: 'Account Creation' },
        },
      },
    },
    questionnaire: {
      meta: { total: 15 },
      initial: 'age',
      states: {
        age: {
          on: {
            next: [
              { guard: 'isUnderAge', target: 'rejectedUnderAge' },
              { guard: 'isOverAge', target: 'rejectedOverAge' },
              { target: 'gender', actions: 'storeQuestionnaire' },
            ],
          },
          meta: { step: 5, question: QUESTIONNAIRE_QUESTIONS.birthDate },
        },
        rejectedUnderAge: {
          entry: { type: 'reject', params: { reason: 'under age' } },
          on: { back: { target: 'age', actions: 'clearRejected' } },
          meta: { step: 5 },
        },
        rejectedOverAge: {
          entry: { type: 'reject', params: { reason: 'over age' } },
          on: { back: { target: 'age', actions: 'clearRejected' } },
          meta: { step: 5 },
        },
        gender: {
          on: {
            back: { target: 'age' },
            male: {
              target: 'usingGLP1',
              actions: {
                type: 'storeQuestionnaire',
                params: { gender: 'male' },
              },
            },
            female: {
              target: 'isPregnant',
              actions: {
                type: 'storeQuestionnaire',
                params: { gender: 'female' },
              },
            },
          },
          meta: { step: 7, question: QUESTIONNAIRE_QUESTIONS.gender },
        },
        isPregnant: {
          on: {
            no: {
              target: 'usingGLP1',
              actions: {
                type: 'storeQuestionnaire',
                params: { isPregnant: 'no' },
              },
            },
            yes: {
              target: 'rejectedIsPregnant',
              actions: {
                type: 'storeQuestionnaire',
                params: { isPregnant: 'yes' },
              },
            },
            back: { target: 'gender' },
          },
          meta: { step: 7, question: QUESTIONNAIRE_QUESTIONS.isPregnant },
        },
        rejectedIsPregnant: {
          entry: { type: 'reject', params: { reason: 'is pregnant' } },
          on: {
            back: {
              target: 'isPregnant',
              actions: 'clearRejected',
            },
          },
          meta: {
            step: 7,
          },
        },
        usingGLP1: {
          on: {
            no: {
              target: 'haveDiabetes',
              actions: {
                type: 'storeQuestionnaire',
                params: { usingGLP1: 'no' },
              },
            },
            yes: {
              target: 'haveDiabetes',
              actions: {
                type: 'storeQuestionnaire',
                params: { usingGLP1: 'yes' },
              },
            },
            back: { target: 'gender' },
          },
          meta: { step: 7, question: QUESTIONNAIRE_QUESTIONS.usingGLP1 },
        },
        haveDiabetes: {
          on: {
            no: {
              target: 'eligible',
              actions: {
                type: 'storeQuestionnaire',
                params: { haveDiabetes: 'no' },
              },
            },
            yes: {
              target: 'rejectedPriorConditions',
              actions: {
                type: 'storeQuestionnaire',
                params: { haveDiabetes: 'yes' },
              },
            },
            back: { target: 'usingGLP1' },
          },
          meta: { step: 7, question: QUESTIONNAIRE_QUESTIONS.haveDiabetes },
        },
        rejectedPriorConditions: {
          entry: { type: 'reject', params: { reason: 'has prior conditions' } },
          on: {
            back: { target: 'haveDiabetes', actions: 'clearRejected' },
          },
          meta: { step: 7 },
        },
        eligible: {
          on: {
            back: { target: 'haveDiabetes' },
            next: { target: 'doctorVisits' },
          },
          meta: { step: 8 },
        },
        doctorVisits: {
          on: {
            no: {
              target: 'recommendSeeDoctor',
              actions: {
                type: 'storeQuestionnaire',
                params: { doctorVisits: 'no' },
              },
            },
            yes: {
              target: 'qualifyingConditions',
              actions: {
                type: 'storeQuestionnaire',
                params: { doctorVisits: 'yes' },
              },
            },
            back: { target: 'eligible' },
          },
          meta: { step: 9, question: QUESTIONNAIRE_QUESTIONS.doctorVisits },
        },
        recommendSeeDoctor: {
          on: {
            back: { target: 'doctorVisits' },
            next: { target: 'qualifyingConditions' },
          },
          meta: { step: 9 },
        },
        qualifyingConditions: {
          on: {
            back: { target: 'doctorVisits' },
            next: { target: 'height', actions: 'storeQuestionnaire' },
          },
          meta: {
            step: 10,
            question: QUESTIONNAIRE_QUESTIONS.qualifyingConditions,
          },
        },
        height: {
          on: {
            back: { target: 'qualifyingConditions' },
            next: { target: 'weight', actions: 'storeQuestionnaire' },
          },
          meta: { step: 11, question: QUESTIONNAIRE_QUESTIONS.height },
        },
        weight: {
          on: {
            back: { target: 'height' },
            next: { target: 'desiredWeight', actions: 'calculateAndStoreBMI' },
          },
          meta: { step: 12, question: QUESTIONNAIRE_QUESTIONS.weight },
        },
        desiredWeight: {
          on: {
            back: { target: 'weight' },
            next: { target: 'objectives', actions: 'storeQuestionnaire' },
          },
          meta: { step: 13, question: QUESTIONNAIRE_QUESTIONS.desiredWeight },
        },
        objectives: {
          on: {
            back: { target: 'desiredWeight' },
            next: [
              {
                guard: 'needsMedicalNecessity',
                target: 'rejectedMedicalNecessity',
                actions: 'storeQuestionnaire',
              },
              {
                target: 'haveAllergies',
                actions: 'storeQuestionnaire',
              },
            ],
          },
          meta: { step: 14, question: QUESTIONNAIRE_QUESTIONS.objectives },
        },
        rejectedMedicalNecessity: {
          entry: { type: 'reject', params: { reason: 'medical necessity' } },
          on: {
            back: { target: 'objectives', actions: 'clearRejected' },
          },
          meta: { step: 14 },
        },
        haveAllergies: {
          on: {
            no: {
              target: 'medications',
              actions: {
                type: 'storeQuestionnaire',
                params: { allergies: [], hasAllergies: 'no' },
              },
            },
            yes: {
              target: 'selectAllergies',
              actions: {
                type: 'storeQuestionnaire',
                params: { hasAllergies: 'yes' },
              },
            },
            back: { target: 'objectives' },
          },
          meta: { step: 15, question: QUESTIONNAIRE_QUESTIONS.hasAllergies },
        },
        medications: {
          on: {
            back: { target: 'haveAllergies' },
            next: {
              target: 'medicalConditions',
              actions: 'storeQuestionnaire',
            },
          },
          meta: { step: 16, question: QUESTIONNAIRE_QUESTIONS.medications },
        },
        selectAllergies: {
          on: {
            back: { target: 'haveAllergies' },
            next: { target: 'medications', actions: 'storeQuestionnaire' },
          },
          meta: { step: 15, question: QUESTIONNAIRE_QUESTIONS.allergies },
        },
        medicalConditions: {
          on: {
            back: { target: 'medications' },
            next: {
              target: 'additionalInformation',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 17,
            question: QUESTIONNAIRE_QUESTIONS.medicalConditions,
          },
        },
        additionalInformation: {
          on: {
            back: { target: 'medicalConditions' },
            next: { target: 'finished', actions: 'storeQuestionnaire' },
          },
          meta: {
            step: 18,
            question: QUESTIONNAIRE_QUESTIONS.additionalInformation,
          },
        },
        finished: {
          entry: { type: 'complete', params: { completed: true } },
          always: '#onboarding-v3.selectTreatmentType',
        },
      },
    },
    selectTreatmentType: {
      on: {
        next: { target: 'selectTreatment', actions: 'store' },
        back: { target: 'questionnaire.additionalInformation' },
      },
      meta: { step: 1, name: 'Virtual Doctor Visit' },
    },
    selectTreatment: {
      on: {
        next: [
          {
            guard: 'hasSsnSuccess',
            target: 'uploadFacePhoto',
            actions: 'store',
          },
          {
            guard: 'hasSsnCheckFailed',
            target: 'uploadIDPhoto',
            actions: 'store',
          },
          {
            target: 'identityVerification',
            actions: 'store',
          },
        ],
        back: { target: 'selectTreatmentType' },
      },
      meta: { step: 2, name: 'Virtual Doctor Visit' },
    },
    identityVerification: {
      on: {
        next: { target: 'ssnCheck', actions: 'clearSsnVerificationState' },
        back: { target: 'selectTreatment' },
      },
      meta: { step: 3, name: 'Identity Verification' },
    },
    ssnCheck: {
      on: {
        submit: { target: 'verifySsn', actions: 'storeSsnData' },
        skip: { target: 'uploadIDPhoto' },
        back: { target: 'identityVerification' },
      },
      meta: { step: 4, name: 'Identity Verification' },
    },
    verifySsn: {
      invoke: {
        src: 'verifySsn',
        input: ({ context, event }) => {
          const lastFourSSN =
            event.type === 'submit' && event.subtype === 'ssnCheck'
              ? event.value.lastFourSSN
              : context.lastFourSSN;

          return {
            lastFourSSN,
            firstName: context.firstName,
            lastName: context.lastName,
            phone: context.phone,
            dateOfBirth: context.questionnaire.birthDate,
          };
        },
        onDone: {
          target: 'uploadFacePhoto',
          actions: {
            type: 'storeSsnSuccess',
            params: ({ event }) => ({ output: event.output }),
          },
        },
        onError: {
          target: 'uploadIDPhoto',
          actions: 'storeSsnFailure',
        },
      },
    },
    uploadIDPhoto: {
      on: {
        next: { target: 'uploadFacePhoto', actions: 'store' },
        back: { target: 'selectTreatment' },
      },
      meta: { step: 7, name: 'Virtual Doctor Visit' },
    },
    uploadFacePhoto: {
      on: {
        next: { target: 'visitCompletion', actions: 'store' },
        back: [
          {
            guard: 'hasSsnSuccess',
            target: 'selectTreatment',
          },
          {
            target: 'uploadIDPhoto',
          },
        ],
      },
      meta: { step: 8, name: 'Virtual Doctor Visit' },
    },
    visitCompletion: {
      on: {
        next: { target: 'summary' },
        back: { target: 'uploadFacePhoto' },
      },
      meta: { step: 9, name: 'Checkout' },
    },
    summary: {
      on: {
        next: { target: 'shipping' },
        back: { target: 'visitCompletion' },
      },
      meta: { step: 10, name: 'Checkout' },
    },
    shipping: {
      on: {
        next: { target: 'payment', actions: 'store' },
        back: { target: 'summary' },
      },
      meta: { step: 11, name: 'Checkout' },
    },
    payment: {
      on: {
        update: { actions: 'storeClientSecret' },
        complete: { target: 'onboarded' },
        back: { target: 'shipping' },
      },
      meta: { step: 12, name: 'Checkout' },
    },
    onboarded: {
      type: 'final',
      meta: { step: 13 },
    },
  },
});
