import { IsNotEmpty, Matches, MaxLength } from 'class-validator';

export const specialCharsRegex =
  /^[a-zA-Z0-9!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~\s]*$/;

export class PreSignupNameDto {
  @IsNotEmpty()
  @Matches(specialCharsRegex, {
    message: 'First name contains invalid characters',
  })
  @MaxLength(35)
  firstName: string;

  @IsNotEmpty()
  @Matches(specialCharsRegex, {
    message: 'Last name contains invalid characters',
  })
  @MaxLength(35)
  lastName: string;
}
