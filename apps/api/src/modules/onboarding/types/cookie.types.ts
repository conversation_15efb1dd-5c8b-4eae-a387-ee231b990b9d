import type { Snapshot<PERSON>rom } from 'xstate';

import type {
  ONBOARDING_MACHINE_REGISTRY,
  OnboardingVersion,
} from '../states/versions';

/**
 * Type mapping for version to state machine snapshot using the registry
 * This automatically supports new versions when they're added to the registry
 */
export type OnboardingStateSnapshot<T extends OnboardingVersion> =
  T extends keyof typeof ONBOARDING_MACHINE_REGISTRY
    ? SnapshotFrom<(typeof ONBOARDING_MACHINE_REGISTRY)[T]>
    : never;

/**
 * Union type of all possible onboarding snapshots
 * Use this instead of AnyMachineSnapshot for type safety
 */
export type AnyOnboardingSnapshot = {
  [K in OnboardingVersion]: SnapshotFrom<
    (typeof ONBOARDING_MACHINE_REGISTRY)[K]
  >;
}[OnboardingVersion];

/**
 * Base cookie structure without version-specific snapshot
 */
export interface BaseOnboardingCookie {
  startedAt: string;
  currentState?: string;
  authTokens?: OnboardingAuthTokens;
}

/**
 * Version-specific cookie with proper snapshot typing
 */
export interface VersionedOnboardingCookie<T extends OnboardingVersion>
  extends BaseOnboardingCookie {
  version: T;
  stateSnapshot?: OnboardingStateSnapshot<T>;
}

/**
 * Generic cookie with any snapshot type for when version is not known statically
 */
export interface GenericOnboardingCookie extends BaseOnboardingCookie {
  version: OnboardingVersion;
  stateSnapshot?: AnyOnboardingSnapshot;
}

/**
 * Union type for all possible cookie versions
 * This will automatically expand as new versions are added
 */
export type OnboardingCookie = {
  [K in OnboardingVersion]: VersionedOnboardingCookie<K>;
}[OnboardingVersion];

/**
 * Auth tokens structure for authenticated onboarding cookie
 */
export interface OnboardingAuthTokens {
  accessToken: string;
  refreshToken: string;
  role: string;
  patientId: string;
}

/**
 * Type guard to check if a cookie has auth tokens
 */
export function isAuthenticatedCookie(
  cookie: any,
): cookie is BaseOnboardingCookie & { authTokens: OnboardingAuthTokens } {
  return (
    cookie &&
    typeof cookie === 'object' &&
    'authTokens' in cookie &&
    cookie.authTokens &&
    typeof cookie.authTokens === 'object' &&
    'accessToken' in cookie.authTokens &&
    'refreshToken' in cookie.authTokens &&
    'role' in cookie.authTokens &&
    'patientId' in cookie.authTokens
  );
}
