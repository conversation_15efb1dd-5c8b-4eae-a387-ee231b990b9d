import type { CookieOptions } from 'express';
import type { IncomingHttpHeaders } from 'node:http';

import type { PreSignupResponse } from '../use-cases/onboarding-pre-signup-base.use-case';
import type { OnboardingCookie } from './cookie.types';

/**
 * Standard input for pre-signup use cases
 */
export interface PreSignupUseCaseInput<T = any> {
  cookie: OnboardingCookie | null;
  data: {
    event: string;
    value?: T;
  };
  headers?: IncomingHttpHeaders;
}

/**
 * Cookie update instructions
 */
export interface CookieUpdate {
  action: 'set' | 'clear';
  data?: OnboardingCookie;
  options?: CookieOptions;
}

/**
 * Standard output for pre-signup use cases
 */
export interface PreSignupUseCaseOutput {
  response: PreSignupResponse;
  cookieUpdate?: CookieUpdate;
}
