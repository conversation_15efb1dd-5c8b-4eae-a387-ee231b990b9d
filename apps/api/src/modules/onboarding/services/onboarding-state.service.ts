import type { OnboardingLegacyV1Context } from '@modules/onboarding/states/versions/types/onboarding-legacy-v1.types';
import type { OnboardingLegacyV2Context } from '@modules/onboarding/states/versions/types/onboarding-legacy-v2.types';
import type { OnboardingV1Context } from '@modules/onboarding/states/versions/types/onboarding-v1.types';
import type { OnboardingV2Context } from '@modules/onboarding/states/versions/types/onboarding-v2.types';
import type { OnboardingV3Context } from '@modules/onboarding/states/versions/types/onboarding-v3.types';
import {
  AnyOnboardingInput,
  ONBOARDING_MACHINE_REGISTRY,
  OnboardingVersion,
} from '@modules/onboarding/states/versions';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { Injectable } from '@nestjs/common';
import { createActor } from 'xstate';

// Type mapping for onboarding version to context type
export interface OnboardingContextMap {
  v1: OnboardingV1Context;
  v2: OnboardingV2Context;
  v3: OnboardingV3Context;
  'legacy-v1': OnboardingLegacyV1Context;
  'legacy-v2': OnboardingLegacyV2Context;
}

// Generic type to get context type from version
export type OnboardingContextForVersion<T extends OnboardingVersion> =
  OnboardingContextMap[T];

// Generic return type for getCurrentOnboardingState
export interface OnboardingStateResult<T extends OnboardingVersion> {
  state: any;
  context: OnboardingContextForVersion<T>;
  events: string[];
  stepName: string;
  percentage: number;
}

// Generic OnboardingActor type that preserves version information
export interface OnboardingActorForVersion<T extends OnboardingVersion> {
  getSnapshot(): {
    context: OnboardingContextForVersion<T>;
    value: any;
    status: string;
    can: (event: any) => boolean;
    machine: any;
  };
  send: (event: any) => void;
  start?: () => OnboardingActorForVersion<T>;
}

// Base OnboardingActor type for backward compatibility - covers all versions
export type OnboardingActor = OnboardingActorForVersion<OnboardingVersion>;

// OnboardingSnapshot represents the serialized state stored in database
// Using 'any' for now since our simplified actor interface returns a different shape
// than the full XState snapshot, but persistence layer expects the full snapshot
export type OnboardingSnapshot = any;

export type OnboardingProfile = Awaited<
  ReturnType<OnboardingStateService['getCurrentOnboardingState']>
>;

// Version ordering for comparison (higher number = newer version)
export const VERSION_ORDER: Record<OnboardingVersion, number> = {
  'legacy-v1': 1,
  'legacy-v2': 2,
  v1: 3,
  v2: 4,
  v3: 5,
} as const;

// Feature capabilities by version (for documentation and future capability checking)
export interface OnboardingFeatures {
  hasSSNVerification: boolean;
  hasBMICalculation: boolean;
  hasObjectivesInQuestionnaire: boolean;
  isModernOnboarding: boolean; // v1+ vs legacy
}

export const VERSION_FEATURES: Record<OnboardingVersion, OnboardingFeatures> = {
  'legacy-v1': {
    hasSSNVerification: false,
    hasBMICalculation: false,
    hasObjectivesInQuestionnaire: false,
    isModernOnboarding: false,
  },
  'legacy-v2': {
    hasSSNVerification: false,
    hasBMICalculation: false,
    hasObjectivesInQuestionnaire: true,
    isModernOnboarding: false,
  },
  v1: {
    hasSSNVerification: false,
    hasBMICalculation: false,
    hasObjectivesInQuestionnaire: false,
    isModernOnboarding: true,
  },
  v2: {
    hasSSNVerification: true,
    hasBMICalculation: false,
    hasObjectivesInQuestionnaire: false,
    isModernOnboarding: true,
  },
  v3: {
    hasSSNVerification: true,
    hasBMICalculation: true,
    hasObjectivesInQuestionnaire: false,
    isModernOnboarding: true,
  },
} as const;

// Version comparison utilities
export function compareVersions(
  version1: OnboardingVersion,
  version2: OnboardingVersion,
): number {
  return VERSION_ORDER[version1] - VERSION_ORDER[version2];
}

export function isVersionAtLeast(
  version: OnboardingVersion,
  minimumVersion: OnboardingVersion,
): boolean {
  return VERSION_ORDER[version] >= VERSION_ORDER[minimumVersion];
}

export function hasCapability<K extends keyof OnboardingFeatures>(
  version: OnboardingVersion,
  capability: K,
): boolean {
  return VERSION_FEATURES[version][capability];
}

@Injectable()
export class OnboardingStateService {
  private readonly SECTION_NAMES = {
    preSignup: 'Account Creation',
    questionnaire: 'Health Questionnaire',
    selectTreatmentType: 'Select Your Medication',
    selectTreatment: 'Select Your Medication',
    identityVerification: 'Identity Verification',
    ssnCheck: 'Identity Verification',
    ssnSuccess: 'Identity Verification',
    info: 'Virtual Doctor Visit',
    uploadIdPhoto: 'Upload Your Photos',
    uploadFacePhoto: 'Upload Your Photos',
    shippingInfo: 'Checkout',
    paymentInfo: 'Checkout',
    reviewOrder: 'Checkout',
    confirmPayment: 'Checkout',
    processingPayment: 'Checkout',
    onboarded: 'Completed',
  };

  constructor() {}

  /**
   * Get the default onboarding version for new patients
   * This can be enhanced later to support A/B testing, feature flags, etc.
   */
  getDefaultOnboardingVersion(): OnboardingVersion {
    return 'v3';
  }

  /**
   * Builds the initial states for the onboarding machine and the current questionnaire,
   * and it's persisted to the database when a new patient is created
   */
  async buildInitialStates(onboardingVersion?: OnboardingVersion) {
    const version = onboardingVersion || this.getDefaultOnboardingVersion();
    const onboarding = await this.buildVersionedOnboardingMachine(version);
    return onboarding.getSnapshot();
  }

  async getCurrentOnboardingActor<T extends OnboardingVersion>(
    version: T,
    snapshot: AnyOnboardingSnapshot,
    actors?: any,
  ): Promise<OnboardingActorForVersion<T>> {
    // Validate version exists in registry
    if (!(version in ONBOARDING_MACHINE_REGISTRY)) {
      const availableVersions = Object.keys(ONBOARDING_MACHINE_REGISTRY).join(
        ', ',
      );
      throw new Error(
        `Unknown onboarding version: ${version}. Available versions: ${availableVersions}`,
      );
    }

    return (await this.buildVersionedOnboardingMachine(
      version,
      snapshot,
      actors,
    )) as OnboardingActorForVersion<T>;
  }

  performTransition<T extends OnboardingVersion>(
    actor: OnboardingActorForVersion<T>,
    eventName: string,
    data?: any,
  ): OnboardingActorForVersion<T> {
    let snapshot = actor.getSnapshot();
    const event = data ? { type: eventName, value: data } : { type: eventName };
    if (!snapshot.can(event)) {
      throw new Error('Invalid transition');
    }
    actor.send(event);

    snapshot = actor.getSnapshot();
    if (snapshot.status === 'error') {
      throw new Error(
        `Error after transition: ${JSON.stringify(snapshot.value)}`,
      );
    }
    return actor;
  }

  private getStatePath(stateValue: any): string[] {
    if (typeof stateValue === 'string') {
      return [stateValue];
    }
    // Handle nested state objects like { questionnaire: 'age' }
    const keys = Object.keys(stateValue);
    if (keys.length === 1) {
      return [keys[0], ...this.getStatePath(stateValue[keys[0]])];
    }
    return [];
  }

  private getStateMetadata(snapshot: any): {
    stepName: string;
    percentage: number;
  } {
    const stateValue = snapshot.value;

    // Handle final state
    if (stateValue === 'onboarded') {
      return {
        stepName: this.SECTION_NAMES['onboarded'],
        percentage: 100,
      };
    }

    // Get state path (e.g., ['questionnaire', 'age'] or ['preSignup', 'stateSelection'])
    const statePath = this.getStatePath(stateValue);
    const rootState = statePath[0];

    // Navigate through the state configuration to find the current state's metadata
    let absoluteStep = 0;
    let name = '';
    let stateConfig = snapshot.machine.config.states;

    for (let i = 0; i < statePath.length; i++) {
      const stateName = statePath[i];
      if (stateConfig && stateConfig[stateName]) {
        // Get both step and name from the current state's meta if they exist
        if (stateConfig[stateName]?.meta) {
          absoluteStep = stateConfig[stateName].meta.step || absoluteStep;
          name = stateConfig[stateName].meta.name || name;
        }

        // Navigate to nested states for the next iteration
        if (i < statePath.length - 1 && stateConfig[stateName].states) {
          stateConfig = stateConfig[stateName].states;
        }
      }
    }

    // If no name was found in meta, fall back to SECTION_NAMES
    if (!name) {
      name = this.SECTION_NAMES[rootState] || '';
    }

    // Calculate timeline-based percentage
    let percentage = 0;

    if (rootState === 'preSignup') {
      // Timeline 1: Pre-signup (4 steps)
      const currentStep = Math.min(absoluteStep, 4);
      percentage = Math.round((currentStep / 4) * 100);
    } else {
      // Timeline 2: Main onboarding (questionnaire + post-questionnaire = 24 steps)
      let currentStep = 0;

      if (rootState === 'questionnaire') {
        // Questionnaire steps: absolute steps 5-19 map to timeline steps 1-15
        currentStep = absoluteStep - 4; // Step 5 becomes step 1, step 19 becomes step 15
      } else {
        // Post-questionnaire states
        const postQuestionnaireStepMap: Record<string, number> = {
          selectTreatmentType: 16,
          selectTreatment: 17,
          info: 18,
          uploadIDPhoto: 19,
          uploadFacePhoto: 20,
          visitCompletion: 21,
          summary: 22,
          shipping: 23,
          payment: 24,
        };

        currentStep = postQuestionnaireStepMap[rootState] || 16;
      }

      percentage = Math.round((currentStep / 24) * 100);
    }

    return {
      stepName: name,
      percentage,
    };
  }

  async getCurrentOnboardingState<T extends OnboardingVersion>(
    version: T,
    snapshot: any,
  ): Promise<OnboardingStateResult<T>> {
    const actor = await this.getCurrentOnboardingActor(version, snapshot);

    const onboardingSnapshot = actor.getSnapshot();
    const { stepName, percentage } = this.getStateMetadata(onboardingSnapshot);

    // Get available events from the current state without executing guards
    const stateValue = onboardingSnapshot.value;
    const statePath = this.getStatePath(stateValue); // Use existing helper method

    // Navigate the machine configuration to find the current state's events
    let events: string[] = [];
    try {
      // Access the machine config through the actor
      const machineConfig = (actor as any).logic?.config;

      if (machineConfig?.states) {
        // Navigate to the current state node
        let currentStateConfig = machineConfig.states;

        for (const key of statePath) {
          if (currentStateConfig[key]) {
            // Get events from the current level
            const stateNode = currentStateConfig[key];
            if (stateNode?.on) {
              const stateEvents = Object.keys(stateNode.on).filter(
                (event) => event !== '',
              );
              events.push(...stateEvents);
            }

            // Navigate deeper if there are nested states
            currentStateConfig = currentStateConfig[key].states || {};
          }
        }
      }
    } catch (error) {
      console.warn('Could not extract events from machine config:', error);
      events = [];
    }

    events = [...new Set(events)];

    return {
      state: onboardingSnapshot.value,
      context: onboardingSnapshot.context,
      events,
      stepName,
      percentage,
    };
  }

  private async buildVersionedOnboardingMachine(
    version: OnboardingVersion,
    snapshot?: AnyOnboardingSnapshot,
    actors?: any,
  ) {
    const versionedMachine = ONBOARDING_MACHINE_REGISTRY[version];

    if (!versionedMachine) {
      throw new Error(`Onboarding version ${version} not found`);
    }

    // Provide actors if they are given
    const machine = actors
      ? versionedMachine.provide({ actors })
      : versionedMachine;

    const actor = snapshot
      ? createActor(machine, { snapshot })
      : createActor(machine, {
          input: {
            version,
            existingData: {},
          } as AnyOnboardingInput,
        });

    return actor.start();
  }

  /**
   * Check if a version is at least a minimum version (for version-specific features)
   * @param version The version to check
   * @param minimumVersion The minimum version required
   * @returns True if version >= minimumVersion
   * @example isVersionAtLeast('v3', 'v3') // true (BMI feature available)
   */
  isVersionAtLeast(
    version: OnboardingVersion,
    minimumVersion: OnboardingVersion,
  ): boolean {
    return isVersionAtLeast(version, minimumVersion);
  }

  /**
   * Compare two onboarding versions
   * @param version1 First version to compare
   * @param version2 Second version to compare
   * @returns Negative if version1 < version2, 0 if equal, positive if version1 > version2
   */
  compareVersions(
    version1: OnboardingVersion,
    version2: OnboardingVersion,
  ): number {
    return compareVersions(version1, version2);
  }

  /**
   * Check if a version has a specific capability
   * @param version The version to check
   * @param capability The capability to check for
   * @returns True if the version has the capability
   * @example hasCapability('v3', 'hasBMICalculation') // true
   */
  hasCapability<K extends keyof OnboardingFeatures>(
    version: OnboardingVersion,
    capability: K,
  ): boolean {
    return hasCapability(version, capability);
  }
}
