import type { CookieOptions } from 'express';
import { ContextService } from '@/modules/context/context.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import type {
  AnyOnboardingSnapshot,
  GenericOnboardingCookie,
  OnboardingAuthTokens,
  OnboardingCookie,
} from '../types/cookie.types';

@Injectable()
export class OnboardingCookieService {
  readonly cookieName = 'willow-onboarding';

  constructor(
    private readonly configService: ConfigService,
    private readonly contextService: ContextService,
  ) {}

  /**
   * Generates cookie options based on environment
   */
  generateCookieOptions(): CookieOptions {
    const isProduction = this.configService.get('NODE_ENV') === 'production';

    if (isProduction) {
      return {
        httpOnly: true,
        secure: true,
        sameSite: 'lax',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        path: '/',
      };
    } else {
      return {
        httpOnly: true,
        secure: false,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        path: '/',
      } as CookieOptions;
    }
  }

  /**
   * Serializes cookie data to string
   */
  serializeCookie(data: OnboardingCookie | GenericOnboardingCookie): string {
    return JSON.stringify(data);
  }

  /**
   * Parses cookie string to data
   */
  parseCookie(cookieValue: string | undefined): OnboardingCookie | null {
    if (!cookieValue) {
      return null;
    }

    try {
      return JSON.parse(cookieValue) as OnboardingCookie;
    } catch (error) {
      console.error('Failed to parse onboarding cookie:', error);
      return null;
    }
  }

  /**
   * Creates cookie data with both state and snapshot updates
   */
  updateCookieData(
    cookie: OnboardingCookie | null,
    updates: {
      currentState?: string;
      stateSnapshot?: AnyOnboardingSnapshot;
      authTokens?: OnboardingAuthTokens;
    },
  ): OnboardingCookie {
    if (!cookie) {
      throw new Error('No onboarding cookie data provided');
    }

    return {
      ...cookie,
      ...updates,
    } as OnboardingCookie;
  }

  // Direct cookie operations using context
  /**
   * Gets the onboarding cookie from the current request context
   */
  getCookie(): OnboardingCookie | null {
    const cookieValue = this.contextService.getCookie(this.cookieName);
    return this.parseCookie(cookieValue);
  }

  /**
   * Sets the onboarding cookie in the current response context
   */
  setCookie(data: OnboardingCookie | GenericOnboardingCookie): void {
    const serialized = this.serializeCookie(data);
    const options = this.generateCookieOptions();
    this.contextService.setCookie(this.cookieName, serialized, options);
  }

  /**
   * Clears the onboarding cookie from the current response context
   */
  clearCookie(): void {
    this.contextService.clearCookie(this.cookieName, { path: '/' });
  }

  /**
   * Updates the cookie with new data
   */
  updateCookie(updates: {
    currentState?: string;
    stateSnapshot?: AnyOnboardingSnapshot;
    authTokens?: OnboardingAuthTokens;
  }): void {
    const currentCookie = this.getCookie();
    if (!currentCookie) {
      throw new Error('No onboarding cookie found');
    }

    const updatedCookie = this.updateCookieData(currentCookie, updates);
    this.setCookie(updatedCookie);
  }
}
