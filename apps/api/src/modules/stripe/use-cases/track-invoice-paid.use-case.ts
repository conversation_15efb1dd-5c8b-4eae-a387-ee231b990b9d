import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import {
  getProductForAnalytics,
  titleCase,
} from '@modules/shared/helpers/generic';
import { Injectable, Logger } from '@nestjs/common';
import Stripe from 'stripe';

import { StripeService } from '../service/stripe.service';

const formatPrice = (priceAmount: number) =>
  Number((priceAmount / 100).toFixed(2));

@Injectable()
export class StripeTrackInvoicePaidUseCase {
  private readonly logger = new Logger(StripeTrackInvoicePaidUseCase.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly stripeService: StripeService,
    private readonly auditService: AuditService,
    private readonly segment: SegmentService,
  ) {}

  async execute(invoice: Stripe.Invoice) {
    const { customer, lines } = invoice;
    // get patient data
    const patient = await this.prismaService.patient.findUnique({
      where: { stripeCustomerId: String(customer) },
      include: { user: true },
    });

    if (!patient) {
      this.logger.error('patient not found', {
        stripeCustomerId: String(customer),
      });
      return false;
    }

    const doctor = await this.prismaService.doctor.findUnique({
      where: { id: patient.doctorId },
      include: { user: true },
    });

    const charge = await this.stripeService.retrieveCharge(
      String(invoice.charge),
    );

    const productPrices = await this.prismaService.productPrice.findMany({
      where: { id: { in: lines.data.map((line) => line.price.id) } },
      include: { product: true },
    });

    const products = lines.data.map((item) => {
      const productPrice = productPrices.find((p) => p.id === item.price.id);
      const price = formatPrice(item.amount);
      const amountPaid = formatPrice(
        item.amount -
          item.discount_amounts.reduce((acc, d) => acc + d.amount, 0),
      );

      const {
        productName,
        productClass,
        productType,
        productForm,
        productDosage,
      } = getProductForAnalytics({
        dosageLabel: productPrice?.dosageLabel,
        form: productPrice.product?.form,
        label: productPrice.product?.label,
      });

      void this.segment.track(patient.id, 'productPurchase', {
        properties: {
          productID: item.price.product,
          priceID: item.price.id,
          type:
            item.price.metadata?.type?.toLowerCase() == 'core'
              ? 'Core'
              : 'Add on',
          method: item.price.metadata?.form
            ? titleCase(item.price.metadata?.form)
            : 'N/A',
          price,
          amountPaid,
          productName,
          productClass,
          productType,
          productForm,
          productDosage,
        },
      });

      return {
        id: item.id,
        price: price,
        name:
          item.price?.metadata?.label ||
          item.price?.nickname ||
          item.description,
      };
    });

    await this.segment.identify(patient.id, {
      traits: {
        email: patient.user.email,
      },
    });

    const coreProductPrice = productPrices.find((p) => p.product.isCore);

    const {
      productName,
      productClass,
      productType,
      productForm,
      productDosage,
    } = getProductForAnalytics({
      dosageLabel: coreProductPrice?.dosageLabel,
      form: coreProductPrice?.product.form,
      label: coreProductPrice?.product.label,
    });

    const doctorName = `${doctor.user.firstName} ${doctor.user.lastName}`;

    await this.segment.track(patient.id, 'paymentProcessed', {
      properties: {
        stripeCustomerID: customer,
        state: invoice.customer_shipping?.address?.state || 'N/A',
        doctorName: doctorName,
        doctorID: doctor.id,
        coupon: invoice.discount?.coupon.id,
        transactionID: invoice.id,
        cardType: charge?.payment_method_details?.card?.brand,
        cardLast4: charge?.payment_method_details?.card?.last4,
        ...(invoice.discount && {
          percentOff: invoice.discount?.coupon?.percent_off?.toFixed(2),
          amountOff: invoice.discount?.coupon?.amount_off?.toFixed(2),
        }),
        products: products,
        value: formatPrice(invoice.total),
        amountCharged: formatPrice(invoice.amount_paid),
        productName,
        productClass,
        productType,
        productForm,
        productDosage,
      },
    });

    await this.auditService.append({
      patientId: patient.id,
      resourceType: 'PATIENT',
      resourceId: patient.id,
      action: 'PATIENT_INVOICE_PAID',
      actorId: 'STRIPE',
      actorType: 'SYSTEM',
      details: {
        patientName: `${patient.user.firstName} ${patient.user.lastName}`,
        doctorName: doctorName,
        doctorId: doctor.id,
        stripeCustomerId: patient.stripeCustomerId,
        stripeInvoiceId: invoice.id,
        total: invoice.total,
        amountPaid: invoice.amount_paid,
        invoiceId: invoice.id,
        cardType: charge?.payment_method_details?.card?.brand,
        cardLast4: charge?.payment_method_details?.card?.last4,
        coupon: invoice.discount?.coupon.id,
        products: products,
        items: lines.data.map((item) => ({
          priceId: item.price?.id,
          name: item.description,
          treatmentId: item.metadata.treatmentId,
          prescriptionId: item.metadata.prescriptionId,
          price: formatPrice(item.amount),
        })),
      },
    });
  }
}
