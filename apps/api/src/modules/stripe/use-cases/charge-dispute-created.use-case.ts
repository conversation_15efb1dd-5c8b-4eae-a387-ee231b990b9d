import type { <PERSON><PERSON> } from 'stripe';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import { HttpException, Injectable } from '@nestjs/common';

@Injectable()
export class StripeChargeDisputeCreatedUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly segment: SegmentService,
  ) {}
  async execute(event: Stripe.ChargeDisputeCreatedEvent, patientId: string) {
    const patient = await this.prismaService.patient.findFirst({
      where: { id: patientId },
    });

    if (!patient) {
      throw new HttpException('Patient not found', 404);
    }

    await this.segment.track(patient.id, 'disputeFiled', {
      properties: {},
    });

    return true;
  }
}
