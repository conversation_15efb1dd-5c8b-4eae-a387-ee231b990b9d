import { PrismaService } from '@/modules/prisma/prisma.service';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TreatmentRetryPaymentUseCase {
  constructor(
    private readonly prisma: PrismaService,
    private readonly treatmentService: TreatmentService,
  ) {}

  async execute(stripeCustomerId: string) {
    // Find the patient with the given Stripe customer ID (not cancelled nor deleted)
    const patient = await this.prisma.patient.findFirst({
      where: { stripeCustomerId, canceledAt: null, user: { deletedAt: null } },
    });

    if (!patient) {
      console.log('Patient not found for Stripe customer', stripeCustomerId);
      return;
    }

    // Find all failed treatments for this patient
    const failedTreatments = await this.prisma.treatment.findMany({
      where: { patientId: patient.id, status: 'failed' },
      include: { prescription: true },
    });

    // Iterate over failed treatments
    for (const treatment of failedTreatments) {
      await this.treatmentService.retryPayment(treatment.id, {
        prisma: this.prisma,
      });
    }
  }
}
