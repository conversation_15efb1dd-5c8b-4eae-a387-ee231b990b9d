import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import { Injectable } from '@nestjs/common';
import { Stripe } from 'stripe';

@Injectable()
export class StripeTrackInvoicePaymentFailedUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly segment: SegmentService,
  ) {}

  async execute(data: Stripe.Invoice) {
    if (data.object !== 'invoice') {
      console.error('object is not invoice');
      return false;
    }

    const { customer } = data;

    // get patient data
    const patient = await this.prismaService.patient.findUnique({
      where: { stripeCustomerId: String(customer) },
      include: {
        user: true,
      },
    });

    if (!patient) {
      console.error('patient not found');
      return false;
    }

    const doctor = await this.prismaService.doctor.findUnique({
      where: {
        id: patient.doctorId,
      },
      include: {
        user: true,
      },
    });

    const totalValue = 0;

    const patientName = `${patient.user.firstName} ${patient.user.lastName}`;
    const doctorName = `${doctor.user.firstName} ${doctor.user.lastName}`;

    await this.segment.track(patient.id, 'paymentFailed', {
      properties: {
        patientID: patient.id,
        patientName: patientName,
        doctorName: doctorName,
        doctorID: doctor.id,
        stripeInvoiceId: data.id,
        value: totalValue,
      },
    });
  }
}
