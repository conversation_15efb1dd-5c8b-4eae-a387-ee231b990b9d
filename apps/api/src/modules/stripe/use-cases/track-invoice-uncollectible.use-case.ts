import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import { Injectable, Logger } from '@nestjs/common';
import { Stripe } from 'stripe';

@Injectable()
export class StripeTrackInvoiceUncollectibleUseCase {
  private readonly logger = new Logger(
    StripeTrackInvoiceUncollectibleUseCase.name,
  );

  constructor(
    private readonly prismaService: PrismaService,
    private readonly segment: SegmentService,
  ) {}

  async execute(data: Stripe.Invoice) {
    const { customer, lines } = data;
    // get patient data
    const patient = await this.prismaService.patient.findUnique({
      where: { stripeCustomerId: String(customer) },
      include: {
        user: true,
      },
    });

    if (!patient) {
      this.logger.error('patient not found');
      return false;
    }

    const doctor = await this.prismaService.doctor.findUnique({
      where: {
        id: patient.doctorId,
      },
      include: {
        user: true,
      },
    });

    const products: any = [];
    let totalValue = 0;
    lines.data.forEach((item) => {
      const price = Number(Number(item.amount / 100).toFixed(2));
      totalValue += price;
      products.push({
        id: item.id,
        price: price,
        name: item?.price?.metadata?.label || item.description,
      });
    });

    await this.segment.track(patient.id, 'invoiceUncollectible', {
      properties: {
        patientID: patient.id,
        patientName: `${patient.user.firstName} ${patient.user.lastName}`,
        doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
        doctorID: doctor.id,
        stripeInvoiceId: data.id,
        value: totalValue,
        invoiceStatus: data.status,
        products: products,
      },
    });

    return true;
  }
}
