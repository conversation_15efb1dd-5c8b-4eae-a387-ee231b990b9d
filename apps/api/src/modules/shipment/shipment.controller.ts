import { HeaderAuthGuard } from '@modules/auth/guards/header-auth.guard';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { roles } from '../auth/types/roles';
import { ShipmentUpdateDto } from './dto/shipment-update.dto';
import { ShipmentUpdateUseCase } from './use-cases/shipment-update.use-case';

@Controller('shipment')
export class ShipmentController {
  constructor(private readonly shipmentUpdateUseCase: ShipmentUpdateUseCase) {}

  @Post('webhook')
  @UseGuards(
    HeaderAuthGuard('X-Segment-Tracking-Secret', 'SEGMENT_TRACKING_SECRET'),
  )
  async shipmentUpdate(@Body() requestBody: ShipmentUpdateDto) {
    try {
      return await this.shipmentUpdateUseCase.execute(requestBody);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get(':patientId/status')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles([roles.Patient, roles.Admin])
  async getShippingPackageStatus(@Param('patientId') patientId: string) {
    try {
      return await this.shipmentUpdateUseCase.getShipmentStatus(patientId);
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }
}
