import { HeaderAuthGuard } from '@modules/auth/guards/header-auth.guard';
import { CustomerioDto } from '@modules/chat/dto/customerio.dto';
import { CustomerioSendUseCase } from '@modules/customerio/use-cases/customerio-send.use-case';
import { Body, Controller, Post, UseGuards } from '@nestjs/common';

@UseGuards(HeaderAuthGuard('customerio-secret-key', 'CUSTOMERIO_SECRET_KEY'))
@Controller('customerio')
export class CustomerioController {
  constructor(private readonly customerioSendUseCase: CustomerioSendUseCase) {}

  @Post('/message')
  async sendMessage(@Body() message: CustomerioDto) {
    return this.customerioSendUseCase.execute({
      content: message.content,
      userId: message.userId,
      type: 'system',
    });
  }

  @Post('/note')
  async sendNote(@Body() message: CustomerioDto) {
    return this.customerioSendUseCase.execute({
      content: message.content,
      userId: message.userId,
      type: 'doctor<PERSON>ote',
    });
  }
}
