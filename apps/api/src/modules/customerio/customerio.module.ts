import { ChatModule } from '@modules/chat/chat.module';
import { CustomerioController } from '@modules/customerio/customerioController';
import { CustomerioSendUseCase } from '@modules/customerio/use-cases/customerio-send.use-case';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { forwardRef, Module } from '@nestjs/common';

import { SegmentModule } from '../segment/segment.module';
import { CustomerioService } from './customerio.service';

@Module({
  imports: [
    PrismaModule,
    forwardRef(() => ChatModule),
    forwardRef(() => SegmentModule),
  ],
  providers: [CustomerioService, CustomerioSendUseCase],
  exports: [CustomerioService],
  controllers: [CustomerioController],
})
export class CustomerioModule {}
