import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RegionUS, TrackClient } from 'customerio-node';

@Injectable()
export class CustomerioService {
  private trackClient: TrackClient;

  constructor(private readonly config: ConfigService) {
    const siteId = this.config.getOrThrow<string>('CUSTOMERIO_SITE_ID');
    const apiKey = this.config.getOrThrow<string>(
      'CUSTOMERIO_TRACKING_API_KEY',
    );
    this.trackClient = new TrackClient(siteId, apiKey, {
      region: RegionUS,
    });
  }

  async identify(userId: string, traits: Record<string, any>) {
    await this.trackClient.identify(userId, traits);
  }
}
