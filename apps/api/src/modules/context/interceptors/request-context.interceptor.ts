import type {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  NestInterceptor,
} from '@nestjs/common';
import type { Request, Response } from 'express';
import type { Observable } from 'rxjs';
import { Injectable } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';

import { getClientIp } from '../context.helpers';

/**
 * Interceptor that populates the CLS context with request-specific data.
 * This runs after guards but before route handlers, ensuring context is available
 * throughout the request lifecycle.
 */
@Injectable()
export class RequestContextInterceptor implements NestInterceptor {
  constructor(private readonly cls: ClsService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (context.getType() === 'http') {
      const request = context.switchToHttp().getRequest<Request>();
      const response = context.switchToHttp().getResponse<Response>();

      // Store the full request and response objects for cookie operations
      this.cls.set('request', request);
      this.cls.set('response', response);

      // Keep IP for backward compatibility
      this.cls.set('ip', getClientIp(request));
    }

    return next.handle();
  }
}
