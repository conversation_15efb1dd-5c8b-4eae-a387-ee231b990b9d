import { runInDbTransaction } from '@/helpers/transaction';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { Roles } from '@/modules/auth/types/roles';
import { CreatePatientFollowUpUseCase } from '@/modules/follow-up/use-cases/create-patient-follow-up.use-case';
import { TreatmentUpdatedEvent } from '@/modules/shared/events/treatment-topic.definition';
import { Caller } from '@/modules/shared/types/user/user.types';
import { PrismaService } from '@modules/prisma/prisma.service';
import { TreatmentDto } from '@modules/treatment/dto/treatment.dto';
import {
  TreatmentService,
  TreatmentSnapshot,
} from '@modules/treatment/services/treatment.service';
import { BadRequestException, Injectable } from '@nestjs/common';

@Injectable()
export class TreatmentResumeUseCase {
  constructor(
    private readonly treatmentService: TreatmentService,
    private readonly prisma: PrismaService,
    private readonly auditService: AuditService,
    private readonly createPatientFollowUpUseCase: CreatePatientFollowUpUseCase,
  ) {}

  async execute({ treatmentId }: TreatmentDto, caller: Caller) {
    return runInDbTransaction(this.prisma, async (prisma) => {
      const treatment = await prisma.treatment.findFirstOrThrow({
        where: { id: treatmentId },
      });

      const treatmentEventsToEmit: {
        event: TreatmentUpdatedEvent['event'];
      }[] = [];
      const actor = await this.treatmentService.getActor(
        treatmentId,
        (e) => {
          treatmentEventsToEmit.push(e);
          console.log('[TreatmentResumeUseCase] emitTreatmentEvent', e);
        },
        treatment.state as unknown as TreatmentSnapshot,
      );

      if (!actor.getSnapshot().can({ type: 'resume' })) {
        throw new BadRequestException('Treatment cannot be resumed');
      }

      actor.send({ type: 'resume' });

      const updatedTreatment =
        await this.treatmentService.updateTreatmentRecord(actor, { prisma });
      if (treatment.isCore) {
        await this.createPatientFollowUpUseCase.execute(treatment, {
          prisma,
        });
      }
      for (const { event } of treatmentEventsToEmit) {
        await this.treatmentService.emitTreatmentUpdatedEvent(
          event,
          updatedTreatment,
          { prisma },
        );
      }

      void this.auditService.append({
        patientId: treatment.patientId,
        action: 'TREATMENT_RESUMED',
        actorType: caller.actorType.toUpperCase() as 'DOCTOR' | 'ADMIN',
        actorId: caller.actorId,
        resourceType: 'TREATMENT',
        resourceId: treatmentId,
        details: {},
      });

      return this.treatmentService.getTreatment(actor);
    });
  }
}
