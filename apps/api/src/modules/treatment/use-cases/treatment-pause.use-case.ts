import { runInDbTransaction } from '@/helpers/transaction';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { FollowUpService } from '@/modules/follow-up/services/follow-up.service';
import { TreatmentUpdatedEvent } from '@/modules/shared/events/treatment-topic.definition';
import { Caller } from '@/modules/shared/types/user/user.types';
import { PrismaService } from '@modules/prisma/prisma.service';
import { TreatmentPauseDto } from '@modules/treatment/dto/treatment-pause.dto';
import {
  TreatmentService,
  TreatmentSnapshot,
} from '@modules/treatment/services/treatment.service';
import { BadRequestException, Injectable } from '@nestjs/common';

@Injectable()
export class TreatmentPauseUseCase {
  constructor(
    private readonly treatmentService: TreatmentService,
    private readonly prisma: PrismaService,
    private readonly auditService: AuditService,
    private readonly followUpService: FollowUpService,
  ) {}

  async execute({ treatmentId, date }: TreatmentPauseDto, caller: Caller) {
    return runInDbTransaction(this.prisma, async (prisma) => {
      const treatment = await prisma.treatment.findFirstOrThrow({
        where: { id: treatmentId },
      });

      const activeFollowUp = await this.followUpService.getActive(
        treatment.patientId,
      );
      const treatmentEventsToEmit: {
        event: TreatmentUpdatedEvent['event'];
      }[] = [];
      const actor = await this.treatmentService.getActor(
        treatmentId,
        (e) => {
          treatmentEventsToEmit.push(e);
          console.log('[TreatmentPauseUseCase] emitTreatmentEvent', e);
        },
        treatment.state as unknown as TreatmentSnapshot,
      );

      if (!actor.getSnapshot().can({ type: 'pause' })) {
        throw new BadRequestException('Treatment cannot be paused');
      }

      actor.send({ type: 'pause', until: date ?? null });

      const updatedTreatment =
        await this.treatmentService.updateTreatmentRecord(actor, { prisma });
      if (treatment.isCore && activeFollowUp) {
        await this.followUpService.cancel(activeFollowUp.id, {
          prisma,
        });
      }

      for (const { event } of treatmentEventsToEmit) {
        await this.treatmentService.emitTreatmentUpdatedEvent(
          event,
          updatedTreatment,
          { prisma },
        );
      }

      void this.auditService.append({
        patientId: treatment.patientId,
        action: 'TREATMENT_PAUSED',
        actorType: caller.actorType.toUpperCase() as 'DOCTOR' | 'ADMIN',
        actorId: caller.actorId,
        resourceType: 'TREATMENT',
        resourceId: treatmentId,
        details: {
          until: date ?? null,
        },
      });

      return this.treatmentService.getTreatment(actor);
    });
  }
}
