import { PatientPaymentMethodPersistence } from '@/adapters/persistence/database/patient-payment-method.persistence';
import { PatientPersistence } from '@/adapters/persistence/database/patient.persistence';
import { AdminUseCases } from '@modules/admin/use-cases';
import { AuditLogModule } from '@modules/audit-log/audit-log.module';
import { AuditService } from '@modules/audit-log/audit-log.service';
import { AuthModule } from '@modules/auth/auth.module';
import { AppCacheModule } from '@modules/cache/cache.module';
import { ChatModule } from '@modules/chat/chat.module';
import { DoctorModule } from '@modules/doctor/doctor.module';
import { GetPatientPreSignedUrlUseCase } from '@modules/doctor/use-cases/get-patient-pre-signed-url.use-case';
import { UpdatePatientPhotoUseCase } from '@modules/doctor/use-cases/update-patient-photo.use-case';
import { DosespotService } from '@modules/dosespot/dosespot.service';
import { PatientModule } from '@modules/patient/patient.module';
import { PatientCancelSubscriptionUseCase } from '@modules/patient/use-cases/patient-cancel-subscription.use-case';
import { PatientRestoreSubscriptionUseCase } from '@modules/patient/use-cases/patient-restore-subscription-use-case';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { SegmentModule } from '@modules/segment/segment.module';
import { SesModule } from '@modules/shared/aws/ses/ses.module';
import { SnsModule } from '@modules/shared/aws/sns/sns.module';
import { LoggerModule } from '@modules/shared/logger/logger.module';
import { OutboxerModule } from '@modules/shared/outboxer/outboxer.module';
import { S3Service } from '@modules/shared/services/s3.service';
import { UserForgotPasswordUseCase } from '@modules/shared/use-cases/user-forgot-password-use.case';
import { StripeModule } from '@modules/stripe/stripe.module';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { TreatmentCancelUseCase } from '@modules/treatment/use-cases/treatment-cancel.use-case';
import { forwardRef, Module } from '@nestjs/common';

import { AdminAuthController } from './admin-auth.controller';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';

@Module({
  imports: [
    LoggerModule,
    forwardRef(() => ChatModule),
    PrismaModule,
    AuthModule,
    AuditLogModule,
    StripeModule,
    PatientModule,
    AppCacheModule,
    OutboxerModule,
    SnsModule,
    SesModule,
    DoctorModule,
    SegmentModule,
  ],
  controllers: [AdminController, AdminAuthController],
  providers: [
    AdminService,
    PatientPaymentMethodPersistence,
    PatientCancelSubscriptionUseCase,
    PatientRestoreSubscriptionUseCase,
    PatientPersistence,
    TreatmentService,
    S3Service,
    DosespotService,
    S3Service,
    TreatmentCancelUseCase,
    TreatmentService,
    AuditService,
    UserForgotPasswordUseCase,
    GetPatientPreSignedUrlUseCase,
    UpdatePatientPhotoUseCase,
    ...AdminUseCases,
  ],
  exports: [AdminService],
})
export class AdminModule {}
