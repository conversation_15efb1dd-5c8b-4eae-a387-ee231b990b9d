export interface IntercomWebhookPayload<
  TData extends { type: string; item: Record<string, any> } = {
    type: string;
    item: Record<string, any>;
  },
> {
  type: 'notification_event';
  app_id: string;
  data: TData;
  links: Record<string, any>;
  id: string | null;
  topic: string;
  delivery_attempts: number;
  delivery_status: string | null;
  delivered_at: number;
  first_sent_at: number;
  created_at: number;
  self: string | null;
}

export type IntercomConversationEvent = IntercomWebhookPayload<{
  type: string;
  item: {
    type: 'conversation';
    id: string;
    created_at: number;
    updated_at: number;
    waiting_since: number;
    snoozed_until: number;
    source: {
      type: string;
      id: string;
      delivered_as: string;
    };
    contacts: {
      type: 'contact.list';
      contacts: {
        external_id: string;
        id: string;
        type: 'contact';
      }[];
    };
    teammates: {
      admins: { type: 'admin'; id: string }[];
      type: 'admin.list';
    };
    title: string;
    admin_assignee_id: string;
    team_assignee_id: string;
    custom_attributes: Record<string, any>;
    open: boolean;
    state: string;
    read: boolean;
    tags: {
      type: 'tag';
      id: string;
      name: string;
    }[];
  };
}>;
