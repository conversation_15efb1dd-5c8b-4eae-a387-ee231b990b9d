import { createHmac } from 'crypto';
import { PatientMessageRouterService } from '@modules/chat/services/patient-message-router.service';
import {
  Body,
  Controller,
  Headers,
  HttpCode,
  HttpException,
  HttpStatus,
  Post,
  RawBodyRequest,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

import { ContextPopulationService } from '../context/context.service';
import { LoggerFactory, LoggerService } from '../shared/logger/logger.service';
import { IntercomWebhookDto } from './dto/webhook.dto';
import { IntercomConversationEvent } from './types/webhook';
import { IntercomConversationEventUseCase } from './use-cases/intercom-conversation-event.use-case';

@Controller('intercom')
export class IntercomController {
  private readonly logger: LoggerService;

  constructor(
    private readonly configService: ConfigService,
    private readonly contextPopulate: ContextPopulationService,
    private readonly patientMessageRouterService: PatientMessageRouterService,
    private readonly intercomConversationEventUseCase: IntercomConversationEventUseCase,
    loggerFactory: LoggerFactory,
  ) {
    this.logger = loggerFactory.createLogger(IntercomController.name);
  }

  @Post('webhook')
  @HttpCode(200)
  async handleIntercomWebhook(
    @Body() payload: IntercomWebhookDto,
    @Headers('X-Hub-Signature') signature: string,
    @Req() req: RawBodyRequest<Request>,
  ) {
    //NOTE: always use the raw body for signature validation
    const rawBody: string = req.rawBody.toString('utf8');

    if (process.env.NODE_ENV === 'production')
      this.validateWebhookSignature(signature, rawBody);

    this.contextPopulate.populateActor({ type: 'INTERCOM' });

    try {
      // Log the received webhook for debugging
      this.logger.debug(`Received Intercom webhook`, {
        signature,
        body: payload,
      });

      // Extract the event type from the payload
      const { topic } = payload;

      switch (topic) {
        case 'ping':
          // Handle ping event
          this.logger.log(
            `Received ping from Intercom: ${payload.data.item.message}`,
          );
          break;
        case 'conversation.admin.single.created':
        case 'conversation.admin.replied':
        case 'conversation.user.created':
        case 'conversation.user.replied':
          await this.intercomConversationEventUseCase.execute(
            payload as IntercomConversationEvent,
          );
          break;
        case 'conversation.admin.closed':
          // Handle conversation closed event
          try {
            const conversationEvent = payload as IntercomConversationEvent;
            const conversationId = conversationEvent.data?.item?.source?.id;

            if (!conversationId) {
              this.logger.warn(
                'Received conversation.admin.closed event without conversation ID',
              );
              break;
            }

            this.logger.log(`Intercom conversation closed: ${conversationId}`);
            await this.patientMessageRouterService.markConversationRouterAsClosed(
              conversationId,
            );
          } catch (error) {
            this.logger.error(
              `Error processing conversation.admin.closed event: ${error.message}`,
              error.stack,
            );
          }
          break;
        case 'conversation.admin.opened':
          // Handle conversation reopened event
          try {
            const conversationEvent = payload as IntercomConversationEvent;
            const conversationId = conversationEvent.data?.item?.source?.id;

            if (!conversationId) {
              this.logger.warn(
                'Received conversation.admin.opened event without conversation ID',
              );
              break;
            }

            this.logger.log(
              `Intercom conversation reopened: ${conversationId}`,
            );
            await this.patientMessageRouterService.reopenClosedConversationRouter(
              conversationId,
            );
          } catch (error) {
            this.logger.error(
              `Error processing conversation.admin.opened event: ${error.message}`,
              error.stack,
            );
          }
          break;
        default:
          this.logger.warn(
            `Received unhandled Intercom webhook topic: ${topic}`,
          );
      }

      // Return a successful response to acknowledge receipt
      return { success: true, message: 'Webhook processed successfully' };
    } catch (error) {
      // Log the error but still return a 200 response for business logic errors
      // This allows us to handle errors internally without causing webhook retries
      this.logger.error(
        error,
        {
          signature,
          payload,
        },
        `Error processing Intercom webhook: ${error.message}`,
      );
      throw new HttpException(
        'Error processing Intercom webhook',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private validateWebhookSignature(signature: string, payload: string): void {
    // If webhook security is configured, validate the signature
    const clientSecret = this.configService.get<string>(
      'INTERCOM_CLIENT_SECRET',
    );

    if (!clientSecret) {
      this.logger.error('Intercom client secret not configured');
      throw new HttpException(
        'Intercom client secret not configured',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    if (!signature) {
      throw new UnauthorizedException('Missing webhook signature');
    }

    // Intercom signatures start with 'sha1='
    if (!signature.startsWith('sha1=')) {
      this.logger.error(`Invalid signature format: ${signature}`);
      throw new UnauthorizedException('Invalid signature format');
    }

    // Extract the actual signature (just remove 'sha1=' prefix)
    const actualSignature = signature.substring(5);

    // Compute the signature
    const hmac = createHmac('sha1', clientSecret);
    hmac.update(payload);
    const computedSignature = hmac.digest('hex');

    // Compare signatures
    if (actualSignature !== computedSignature) {
      this.logger.error('Webhook signature validation failed');

      throw new UnauthorizedException('Invalid webhook signature');
    }
  }
}
