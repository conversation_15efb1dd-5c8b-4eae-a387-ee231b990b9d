import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { AuditLogModule } from '../audit-log/audit-log.module';
import { ChatModule } from '../chat/chat.module';
import { ContextModule } from '../context/context.module';
import { PrismaModule } from '../prisma/prisma.module';
import { LoggerModule } from '../shared/logger/logger.module';
import { IntercomController } from './intercom.controller';
import { IntercomService } from './intercom.service';
import { IntercomConversationEventUseCase } from './use-cases/intercom-conversation-event.use-case';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    forwardRef(() => ChatModule),
    ContextModule,
    AuditLogModule,
    LoggerModule,
  ],
  providers: [IntercomService, IntercomConversationEventUseCase],
  controllers: [IntercomController],
  exports: [IntercomService],
})
export class IntercomModule {}
