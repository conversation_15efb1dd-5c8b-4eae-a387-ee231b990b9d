import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { Injectable } from '@nestjs/common';

import { IntercomService } from '../intercom.service';
import { IntercomConversationEvent } from '../types/webhook';

@Injectable()
export class IntercomConversationEventUseCase {
  private readonly logger: LoggerService;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly intercomService: IntercomService,
    private readonly loggerFactory: LoggerFactory,
    private readonly auditService: AuditService,
  ) {
    this.logger = this.loggerFactory.createLogger(
      IntercomConversationEventUseCase.name,
    );
  }
  async execute(conversationEvent: IntercomConversationEvent) {
    const patientId = await this.getPatientId(conversationEvent);
    const conversationId = conversationEvent.data.item.id;

    if (!patientId) {
      this.logger.warn(
        `Intercom: unable to find patientId in conversation event`,
        { conversationId, event: conversationEvent },
      );
      return;
    }

    this.logger.debug(
      `Intercom: Processing Intercom conversation event, patientId: ${patientId}`,
      { patientId, conversationId },
    );

    const message = this.getMessage(conversationEvent);

    if (!message) {
      this.logger.warn(
        `Intercom: unable to find message in conversation event`,
        { conversationId, event: conversationEvent, patientId },
      );
      return;
    }

    this.logger.debug(
      `[Intercom] save INTERCOM_CONVERSATION_MESSAGE_CREATED audit log`,
      {
        patientId,
        conversationId,
        message,
      },
    );

    await this.auditService.append({
      action: 'INTERCOM_CONVERSATION_MESSAGE_CREATED',
      patientId: patientId,
      resourceType: 'CONVERSATION',
      resourceId: conversationId,
      actorType: 'SYSTEM',
      actorId: 'intercom',
      details: {
        content: message.content,
        author: message.author,
        intercomConversationId: conversationId,
        isFirstConversationMessage:
          this.isConversationCreator(conversationEvent),
      },
    });
  }

  private isConversationReply(event: any) {
    return (
      event.topic == 'conversation.admin.replied' ||
      event.topic == 'conversation.user.replied'
    );
  }

  private isConversationCreator(event: any) {
    return (
      event.topic == 'conversation.admin.single.created' ||
      event.topic == 'conversation.user.created'
    );
  }

  private async getPatientId(
    conversationEvent: IntercomConversationEvent,
  ): Promise<string | null> {
    const contacts = conversationEvent.data.item.contacts;

    let patientId: string | undefined;
    for (const contact of contacts.contacts) {
      const patient = await this.prismaService.patient.findFirst({
        where: { intercomContactId: contact.id },
      });

      if (patient) {
        patientId = patient.id;
        break;
      }

      const intercomContact = await this.intercomService.findContactById(
        contact.id,
      );

      if (!intercomContact.id || !intercomContact.email) continue;

      const patientByEmail = await this.prismaService.patient.findFirst({
        where: { user: { email: intercomContact.email } },
      });

      if (patientByEmail) {
        await this.prismaService.patient.update({
          where: { id: patientByEmail.id },
          data: { intercomContactId: intercomContact.id },
        });

        patientId = patientByEmail.id;
        break;
      }
    }

    return patientId;
  }

  private getMessage(conversationEvent: any) {
    if (this.isConversationReply(conversationEvent)) {
      const messageData =
        conversationEvent.data.item.conversation_parts.conversation_parts.find(
          (part) => part.part_type == 'comment',
        );
      if (!messageData) return;
      return {
        content: messageData.body,
        author: messageData.author,
      };
    } else if (this.isConversationCreator(conversationEvent)) {
      const messageData = conversationEvent.data.item.source;
      if (!messageData) return;
      return {
        content: `${messageData.subject ? messageData.subject + ' ' : ''}${messageData.body}`,
        author: messageData.author,
      };
    } else {
      return null;
    }
  }
}
