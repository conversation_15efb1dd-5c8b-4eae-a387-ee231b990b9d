import { Roles } from '@/modules/auth/cognito.service';
import { DoctorProfile } from '@adapters/persistence/database/doctor.persistence';
import { PatientDashboardProfile } from '@adapters/persistence/database/patient.persistence';
import { OnboardingProfile } from '@modules/onboarding/services/onboarding-state.service';
import { UserType } from '@prisma/client';

export type UserSignInInput = {
  email: string;
  password: string;
};

export type BaseSignInOutput = {
  accessToken: string;
  refreshToken: string;
  role: string;
};

export type PatientSignInOutput = {
  status: string;
  patientId: string;
  onboarding?: OnboardingProfile;
  dashboard?: PatientDashboardProfile;
  getStarted: {
    email: string;
    phone?: string;
    state?: string;
    firstName?: string;
    lastName?: string;
    gender?: string;
    birthday?: string;
  };
} & BaseSignInOutput;

export type AdminSignInOutput = {
  id: string;
  type: UserType;
  firstName: string;
  lastName: string;
  email: string;
  phone: string | null;
  createdAt: Date;
  deletedAt: Date | null;
} & BaseSignInOutput;

export type DoctorSignInOutput = BaseSignInOutput & { doctor: DoctorProfile };

export type Caller = {
  actorType: Roles;
  actorId: string;
};
