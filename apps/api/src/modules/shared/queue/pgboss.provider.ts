import { PrismaTransactionalClient } from '@/modules/prisma/prisma.service';
import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import * as PgBoss from 'pg-boss';

// import { QUEUE_MODULE_OPTIONS, QueueModuleOptions } from './queue.module';

@Injectable()
export default class PgBossClient implements OnModuleInit, OnModuleDestroy {
  public pgboss: PgBoss;

  constructor() {
    // private readonly options: QueueModuleOptions, // @Inject(QUEUE_MODULE_OPTIONS)
    this.pgboss = new PgBoss({
      connectionString: process.env.DATABASE_URL!,
      application_name: 'willow-api',
      schema: 'pgboss',
      deleteAfterDays: 15,
    });
    void this.connect();
  }

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.stop();
  }

  private async connect() {
    this.pgboss.on('error', (error) =>
      console.error('[PgBoss] error:', error.message),
    );
    await this.pgboss.start();
  }

  private async stop() {
    await this.pgboss.stop();
  }

  public mapPrismaToPgBoss(prisma: PrismaTransactionalClient): PgBoss.Db {
    return {
      async executeSql(sql: string, values: unknown[]) {
        const result = await prisma.$queryRawUnsafe<{ id: string }[]>(
          sql,
          ...values,
        );
        return { rows: result };
      },
    };
  }

  async isAlive(): Promise<boolean> {
    try {
      await this.pgboss.getQueues();
      return Promise.resolve(true);
    } catch (error) {
      return Promise.resolve(false);
    }
  }
}
