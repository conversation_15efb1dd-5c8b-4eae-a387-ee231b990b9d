import { Inject, Injectable } from '@nestjs/common';

import { PgQueueSendOptions } from './pg-queue.abstract';
import PgBossClient from './pgboss.provider';

export type PgTopicFullMessage<
  TPayload extends object,
  TEvent extends string,
> = {
  metadata: {
    event: TEvent;
    patientId: string;
    topic: string;
  };
  payload: {
    event: TEvent;
    payload: TPayload;
  };
};

@Injectable()
export abstract class PgTopic<TPayload extends object, TEvents extends string> {
  @Inject()
  private readonly pgbossClient!: PgBossClient;

  constructor(public readonly name: string) {}

  protected async send(
    event: TEvents,
    patientId: string,
    payload: TPayload,
    { prisma, ...options }: PgQueueSendOptions = {},
  ) {
    return this.pgbossClient.pgboss.publish(
      this.name,
      {
        metadata: { patientId, topic: this.name, event },
        payload: {
          event,
          payload,
        },
      } satisfies PgTopicFullMessage<TPayload, TEvents>,
      {
        db: prisma ? this.pgbossClient.mapPrismaToPgBoss(prisma) : undefined,
        ...options,
      },
    );
  }

  async subscribe(consumerName: string) {
    await this.pgbossClient.pgboss.subscribe(this.name, consumerName);
  }
}
