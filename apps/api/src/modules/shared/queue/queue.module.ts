import { DynamicModule, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import PgBossClient from './pgboss.provider';

export const QUEUE_MODULE_OPTIONS = 'QUEUE_MODULE_OPTIONS';
export type QueueModuleOptions = {
  postgres: {
    url: string;
  };
};

@Module({})
export class QueueModule {
  static forRoot(options: QueueModuleOptions): DynamicModule {
    return {
      module: QueueModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: QUEUE_MODULE_OPTIONS,
          useValue: options,
        },
        PgBossClient,
      ],
      exports: [PgBossClient],
      global: true,
    };
  }
}
