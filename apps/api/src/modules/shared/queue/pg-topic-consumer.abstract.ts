import { Injectable, OnApplicationBootstrap } from '@nestjs/common';

import { JobMetadata, PgQueue } from './pg-queue.abstract';
import { PgTopic, PgTopicFullMessage } from './pg-topic.abstract';

export type PgTopicMessage<
  TPayload extends object,
  TEvent extends string,
> = PgTopicFullMessage<TPayload, TEvent>['payload'];

export type PgTopicMetadata<TEvent extends string = string> = JobMetadata<
  object,
  PgTopicFullMessage<object, TEvent>['metadata'] & {
    queue: string;
    patientId: string;
  }
>;

@Injectable()
export abstract class PgTopicConsumer<
    TPayload extends object,
    TEvent extends string,
  >
  extends PgQueue<PgTopicMessage<TPayload, TEvent>>
  implements OnApplicationBootstrap
{
  protected abstract onMessage(
    message: PgTopicMessage<TPayload, TEvent>,
    metadata: PgTopicMetadata<TEvent>,
  ): Promise<unknown>;

  protected readonly consumerName: string;
  protected readonly events: TEvent[];

  constructor(
    public readonly pgTopic: PgTopic<TPayload, TEvent>,
    options: {
      consumerName: string;
      events: TEvent[];
    },
  ) {
    super(options.consumerName, { concurrency: 1 });
    this.consumerName = options.consumerName;
    this.events = options.events;
  }

  async onApplicationBootstrap() {
    await super.onApplicationBootstrap();
    await this.pgTopic.subscribe(this.consumerName);
    // TODO: only subscribe to events that are in this.events
  }

  protected async work(
    message: PgTopicMessage<TPayload, TEvent>,
    metadata: PgTopicMetadata<TEvent>,
  ): Promise<unknown> {
    if (!this.events.includes(message.event as TEvent)) {
      return this.pgbossClient.pgboss.deleteJob(
        this.consumerName,
        metadata.pgboss.id,
      );
    }

    return this.onMessage(message, metadata);
  }
}
