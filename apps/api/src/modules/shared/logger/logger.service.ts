import { ContextService } from '@/modules/context/context.service';
import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { OrchestrationService } from '../orchestration/orchestration.service';

@Injectable()
export class LoggerService {
  private readonly logger: Logger;

  constructor(
    private readonly configService: ConfigService,
    private readonly orchestrationService: OrchestrationService,
    private readonly contextService: ContextService,
    private readonly name: string,
  ) {
    this.logger = new Logger(name);
  }
  private enrichContext() {
    if (process.env['ENVIRONMENT'] === 'local') return {};

    const instanceId = OrchestrationService.instanceId;
    return {
      context: {
        name: this.name,
        instance: {
          id: instanceId,
          label: OrchestrationService.instanceLabel,
          version: OrchestrationService.instanceVersion,
          isPrimary: instanceId
            ? OrchestrationService.primaryInstanceId === instanceId
            : false,
        },
        actor: this.contextService.getActor() ?? {},
      },
    };
  }

  log(message: string, context: Record<string, unknown> = {}) {
    const data = { ...context, ...this.enrichContext() };
    if (Object.keys(data).length === 0) {
      this.logger.log(message);
      return;
    }
    this.logger.log({ ...data }, message);
  }

  error(
    error: Error | string,
    context: Record<string, unknown> = {},
    message?: string,
  ) {
    if (process.env['ENVIRONMENT'] === 'local') {
      const m = message ?? (error instanceof Error ? error.message : error);
      this.logger.error(m, error instanceof Error ? error.stack : undefined);
      return;
    }

    this.logger.error(
      { ...context, ...this.enrichContext(), err: error },
      message ?? (error instanceof Error ? error.message : error),
    );
  }

  warn(message: string, context: Record<string, unknown> = {}) {
    const data = { ...context, ...this.enrichContext() };
    if (Object.keys(data).length === 0) {
      this.logger.warn(message);
      return;
    }
    this.logger.warn({ ...data }, message);
  }

  debug(message: string, context: Record<string, unknown> = {}) {
    const data = { ...context, ...this.enrichContext() };
    if (Object.keys(data).length === 0) {
      this.logger.debug(message);
      return;
    }
    this.logger.debug({ ...data }, message);
  }

  verbose(message: string, context: Record<string, unknown> = {}) {
    const data = { ...context, ...this.enrichContext() };
    if (Object.keys(data).length === 0) {
      this.logger.verbose(message);
      return;
    }
    this.logger.verbose({ ...data }, message);
  }
}

@Injectable()
export class LoggerFactory {
  constructor(
    private readonly configService: ConfigService,
    private readonly orchestrationService: OrchestrationService,
    private readonly contextService: ContextService,
  ) {}

  createLogger(name: string): LoggerService {
    return new LoggerService(
      this.configService,
      this.orchestrationService,
      this.contextService,
      name,
    );
  }
}
