import { SetMetadata } from '@nestjs/common';

export const EVENT_LISTENER_METADATA = 'event_listener_metadata';

export interface EventListenerOptions {
  async?: boolean;
  suppressErrors?: boolean;
}

export interface EventPayload {
  eventName: string;
  data: any;
  timestamp: number;
  instanceId: string;
}

export interface EventListenerMetadata {
  eventName: string;
  options: EventListenerOptions;
}

export const OnEventPattern = (
  eventName: string,
  options: EventListenerOptions = {},
) => {
  return SetMetadata(EVENT_LISTENER_METADATA, {
    eventName,
    options,
  });
};
