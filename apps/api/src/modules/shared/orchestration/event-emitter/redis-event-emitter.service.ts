import {
  Inject,
  Injectable,
  Logger,
  OnM<PERSON>ule<PERSON><PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';

import type {
  EventListenerOptions,
  EventPayload,
} from './event-emitter.decorators';
import { EVENT_LISTENER_METADATA } from './event-emitter.decorators';

export const REDIS_EVENT_EMITTER_OPTIONS = 'redis_event_emitter_options';

interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
}

export interface RedisEventEmitterModuleOptions {
  redis: RedisConfig;
  channelPrefix?: string;
}

@Injectable()
export class RedisEventEmitterService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisEventEmitterService.name);
  private publisher: Redis;
  private subscriber: Redis;
  private readonly instanceId = uuidv4();
  private readonly listeners = new Map<
    string,
    Array<{
      handler: (...args: any[]) => any;
      options: EventListenerOptions;
      target: any;
    }>
  >();

  constructor(
    @Inject(REDIS_EVENT_EMITTER_OPTIONS)
    private readonly options: RedisEventEmitterModuleOptions,
    private readonly reflector: Reflector,
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
  ) {
    const redisConfig = {
      host: this.options.redis.host,
      port: this.options.redis.port,
      password: this.options.redis.password,
      db: this.options.redis.db || 0,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    };

    this.publisher = new Redis(redisConfig);
    this.subscriber = new Redis(redisConfig);
  }

  async onModuleInit() {
    await this.discoverEventListeners();
    await this.setupSubscriptions();
  }

  async onModuleDestroy() {
    await this.publisher.quit();
    await this.subscriber.quit();
  }

  private async discoverEventListeners() {
    const providers = this.discoveryService.getProviders();

    const consumerHandlers = providers
      .filter(
        (wrapper) =>
          wrapper.instance && Object.getPrototypeOf(wrapper.instance),
      )
      .flatMap((wrapper: InstanceWrapper) => {
        const { instance } = wrapper;
        const prototype = Object.getPrototypeOf(instance);

        return (
          this.metadataScanner
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            .scanFromPrototype(instance, prototype, (methodName: string) =>
              this.extractConsumerMetadata(instance, methodName),
            )
            .filter(Boolean)
        );
      });

    consumerHandlers.forEach((handler) => {
      if (!handler) return;
      this.addListener(
        handler.eventName,
        handler.instance[handler.methodName].bind(handler.instance) as (
          ...args: any[]
        ) => any,
        handler.options,
        handler.instance,
      );
    });
  }

  private extractConsumerMetadata(instance: any, methodName: string) {
    const metadata = this.reflector.get(
      EVENT_LISTENER_METADATA,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      instance[methodName],
    );

    if (!metadata) {
      return null;
    }
    return {
      instance,
      methodName,
      eventName: metadata.eventName,
      options: metadata.options,
    } as {
      instance: any;
      methodName: string;
      eventName: string;
      options: EventListenerOptions;
    };
  }

  private addListener(
    eventName: string,
    handler: (...args: any[]) => any,
    options: EventListenerOptions,
    target: any,
  ) {
    if (!this.listeners.has(eventName)) {
      this.listeners.set(eventName, []);
    }

    this.listeners.get(eventName)!.push({
      handler,
      options,
      target,
    });

    this.logger.debug(`Added listener for event: ${eventName}`);
  }

  private async setupSubscriptions() {
    const eventNames = Array.from(this.listeners.keys());

    if (eventNames.length === 0) {
      return;
    }

    const channels = eventNames.map((name) => this.getChannelName(name));

    await this.subscriber.subscribe(...channels);

    this.subscriber.on('message', async (channel: string, message: string) => {
      try {
        const eventPayload: EventPayload = JSON.parse(message);

        // Skip events from the same instance to avoid loops
        if (eventPayload.instanceId === this.instanceId) {
          return;
        }

        await this.handleEvent(eventPayload);
      } catch (error) {
        this.logger.error('Error processing event message', error);
      }
    });
  }

  private async handleEvent(payload: EventPayload) {
    const listeners = this.listeners.get(payload.eventName);

    if (!listeners || listeners.length === 0) {
      return;
    }

    const promises = listeners.map(async (listener) => {
      try {
        if (listener.options.async !== false) {
          return await listener.handler(payload.data);
        } else {
          return listener.handler(payload.data);
        }
      } catch (error) {
        if (!listener.options.suppressErrors) {
          this.logger.error(
            `Error in event listener for ${payload.eventName}`,
            error,
          );
        }
      }
    });

    if (listeners.some((l) => l.options.async !== false)) {
      await Promise.all(promises);
    }
  }

  emit(eventName: string, data: any) {
    const payload: EventPayload = {
      eventName,
      data,
      timestamp: Date.now(),
      instanceId: this.instanceId,
    };

    const channel = this.getChannelName(eventName);
    this.publisher.publish(channel, JSON.stringify(payload)).catch((error) => {
      this.logger.error(
        `Failed to publish event "${eventName}" to Redis`,
        error,
      );
    });

    // Also handle locally for immediate processing
    void this.handleLocalEvent(payload);

    this.logger.debug(`Emitted event: ${eventName}`);
  }

  private async handleLocalEvent(payload: EventPayload) {
    const listeners = this.listeners.get(payload.eventName);

    if (!listeners || listeners.length === 0) {
      return;
    }

    for (const listener of listeners) {
      try {
        if (listener.options.async !== false) {
          await listener.handler(payload.data);
        } else {
          listener.handler(payload.data);
        }
      } catch (error) {
        if (!listener.options.suppressErrors) {
          this.logger.error(
            `Error in local event listener for ${payload.eventName}`,
            error,
          );
        }
      }
    }
  }

  private getChannelName(eventName: string): string {
    const prefix = this.options.channelPrefix || 'events';
    return `${prefix}:${eventName}`;
  }

  // Method to manually subscribe to events (alternative to decorators)
  on(
    eventName: string,
    handler: (...args: any[]) => any,
    options: EventListenerOptions = {},
  ) {
    this.addListener(eventName, handler, options, null);

    // If this is the first listener for this event, subscribe to Redis channel
    const listeners = this.listeners.get(eventName);
    if (listeners && listeners.length === 1) {
      const channel = this.getChannelName(eventName);
      void this.subscriber.subscribe(channel);
    }
  }

  // Method to remove listeners
  removeListener(eventName: string, handler: (...args: any[]) => any) {
    const listeners = this.listeners.get(eventName);
    if (!listeners) return;

    const index = listeners.findIndex((l) => l.handler === handler);
    if (index !== -1) {
      listeners.splice(index, 1);

      if (listeners.length === 0) {
        this.listeners.delete(eventName);
        const channel = this.getChannelName(eventName);
        this.subscriber.unsubscribe(channel).catch((err) => {
          this.logger.error(
            `Failed to unsubscribe from Redis channel "${channel}": ${err?.message || err}`,
          );
        });
      }
    }
  }

  // Get all registered event names
  getEventNames(): string[] {
    return Array.from(this.listeners.keys());
  }

  // Get listener count for an event
  listenerCount(eventName: string): number {
    const listeners = this.listeners.get(eventName);
    return listeners ? listeners.length : 0;
  }
}
