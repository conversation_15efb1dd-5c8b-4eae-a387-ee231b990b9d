import { DynamicModule, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DiscoveryModule } from '@nestjs/core';

import {
  REDIS_EVENT_EMITTER_OPTIONS,
  RedisEventEmitterModuleOptions,
  RedisEventEmitterService,
} from './redis-event-emitter.service';

@Module({})
export class EventEmitterModule {
  static forRoot(options: RedisEventEmitterModuleOptions): DynamicModule {
    return {
      module: EventEmitterModule,
      imports: [ConfigModule, DiscoveryModule],
      providers: [
        {
          provide: REDIS_EVENT_EMITTER_OPTIONS,
          useValue: options,
        },
        RedisEventEmitterService,
      ],
      exports: [RedisEventEmitterService],
      global: true,
    };
  }
}
