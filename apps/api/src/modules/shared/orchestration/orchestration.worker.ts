import { CacheService } from '@/modules/cache/cache.service';
import {
  Injectable,
  Lo<PERSON>,
  OnModuleD<PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import { Cron } from '@nestjs/schedule';

import {
  OrchestrationService,
  PRIMARY_INSTANCE_KEY,
} from './orchestration.service';

const HEARTBEAT_INTERVAL_SECONDS = 30;
const PRIMARY_INSTANCE_TIMEOUT_SECONDS = 60;

@Injectable()
export class OrchestrationWorker implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(OrchestrationWorker.name);

  constructor(
    private orchestrationService: OrchestrationService,
    private readonly cache: CacheService,
  ) {}

  async onModuleInit() {
    this.cache
      .get<string>(PRIMARY_INSTANCE_KEY)
      .then((primaryInstance) => {
        OrchestrationService.__setPrimaryInstanceId(primaryInstance);
      })
      .catch((err) => {
        this.logger.error(
          err,
          {
            context: {
              instance: {
                version: OrchestrationService.instanceVersion,
                label: OrchestrationService.instanceLabel,
                id: OrchestrationService.instanceId,
              },
            },
          },
          `Failed to get primary instance on initialization: ${err.message}`,
        );
      });

    this.electPrimaryInstance()
      .then(() => {
        this.logger.log(
          `Orchestration worker initialized with instance ID: ${this.orchestrationService.getInstanceId()}`,
        );
      })
      .catch((err) => {
        this.logger.error(
          err,
          {
            context: {
              instance: {
                version: OrchestrationService.instanceVersion,
                label: OrchestrationService.instanceLabel,
                id: OrchestrationService.instanceId,
              },
            },
          },
          `Failed to elect primary instance on initialization: ${err.message}`,
        );
      });
  }

  onModuleDestroy() {
    this.cache.del(PRIMARY_INSTANCE_KEY).catch((err) => {
      this.logger.error(
        err,
        {
          context: {
            instance: {
              version: OrchestrationService.instanceVersion,
              label: OrchestrationService.instanceLabel,
              id: OrchestrationService.instanceId,
            },
          },
        },
        `Failed to clear primary instance: ${err.message}`,
      );
    });
  }

  @Cron(`*/${HEARTBEAT_INTERVAL_SECONDS} * * * * *`)
  async electPrimaryInstance(): Promise<void> {
    // Try to get the current primary instance from cache
    const primaryInstance = await this.cache.get<string>(PRIMARY_INSTANCE_KEY);
    OrchestrationService.__setPrimaryInstanceId(primaryInstance);

    const instanceId = this.orchestrationService.getInstanceId();

    // Log the current situation
    if (primaryInstance) {
      this.logger.verbose(
        {
          context: {
            instance: {
              version: OrchestrationService.instanceVersion,
              label: OrchestrationService.instanceLabel,
              id: instanceId,
              primary: primaryInstance,
            },
          },
        },
        `Current primary instance: ${primaryInstance}, this instance: ${instanceId}`,
      );
    } else {
      this.logger.log(
        {
          context: {
            instance: {
              version: OrchestrationService.instanceVersion,
              label: OrchestrationService.instanceLabel,
              id: instanceId,
              primary: primaryInstance,
            },
          },
        },
        `No primary instance found, trying to become primary`,
      );
    }

    // If there's no primary instance or if this instance is already primary,
    // set or renew this instance as primary
    if (!primaryInstance || primaryInstance === instanceId) {
      await this.cache.set(
        PRIMARY_INSTANCE_KEY,
        instanceId,
        PRIMARY_INSTANCE_TIMEOUT_SECONDS,
      );

      if (!primaryInstance) {
        // Only log when we first become the primary instance
        this.logger.log(
          {
            context: {
              instance: {
                version: OrchestrationService.instanceVersion,
                label: OrchestrationService.instanceLabel,
                id: OrchestrationService.instanceId,
                primary: true,
              },
            },
          },
          `Instance ${instanceId} elected as primary instance`,
        );
      }
      return;
    }
  }
}
