import { PrismaModule } from '@/modules/prisma/prisma.module';
import { DynamicModule, Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

import { AppCacheModule } from '../../cache/cache.module';
import { EventEmitterModule } from './event-emitter/event-emitter.module';
import { OrchestrationService } from './orchestration.service';
import { OrchestrationWorker } from './orchestration.worker';

export const ORCHESTRATION_MODULE_OPTIONS = 'ORCHESTRATION_MODULE_OPTIONS';
export type OrchestrationModuleOptions = {
  redis: {
    host: string;
    port: number;
  };
};

@Module({})
export class OrchestrationModule {
  static forRoot(options: OrchestrationModuleOptions): DynamicModule {
    return {
      module: OrchestrationModule,
      imports: [
        PrismaModule,
        AppCacheModule,
        ScheduleModule.forRoot(),
        EventEmitterModule.forRoot({
          redis: {
            host: options.redis.host,
            port: options.redis.port,
          },
          channelPrefix: 'api',
        }),
      ],
      providers: [
        {
          provide: ORCHESTRATION_MODULE_OPTIONS,
          useValue: options,
        },
        OrchestrationWorker,
        OrchestrationService,
      ],
      exports: [OrchestrationService],
      global: true,
    };
  }
}
