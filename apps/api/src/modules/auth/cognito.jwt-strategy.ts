import { AuthService } from '@modules/auth/auth.service';
import { roles } from '@modules/auth/types/roles';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import * as jwksRsa from 'jwks-rsa';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { ContextPopulationService } from '../context/context.service';

@Injectable()
export class CognitoJwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    configService: ConfigService,
    private readonly authService: AuthService,
    private readonly contextPopulate: ContextPopulationService,
  ) {
    const region = configService.get('AWS_REGION');
    const userPoolId = configService.get('COGNITO_USER_POOL_ID');
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      issuer: `https://cognito-idp.${region}.amazonaws.com/${userPoolId}`,
      algorithms: ['RS256'],
      secretOrKeyProvider: jwksRsa.passportJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri: `https://cognito-idp.${region}.amazonaws.com/${userPoolId}/.well-known/jwks.json`,
      }),
      passReqToCallback: true,
    });
  }

  /**
   * Validates a given token after basic validation like exp, iss and signature has been checked by library
   * @param payload the tokens payload
   * @returns
   */
  async validate(req: Request, payload: any) {
    if (!payload['cognito:groups']) {
      return false;
    }

    const role = payload['cognito:groups'][0];
    if (!roles[role]) return false;

    // Check for impersonation
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (token) {
      const impersonationData =
        await this.authService.getImpersonationData(token);
      if (
        impersonationData &&
        ['Patient', 'Doctor'].includes(impersonationData.role)
      ) {
        await this.contextPopulate.populateActor_User({
          userId: payload.sub,
          impersonatedBy: impersonationData.userId,
        });
        return {
          userId: impersonationData.userId,
          impersonatedBy: payload.sub,
          role: impersonationData.role,
        };
      }
    }

    await this.contextPopulate.populateActor_User({ userId: payload.sub });
    return { userId: payload.sub, role: role };
  }
}
