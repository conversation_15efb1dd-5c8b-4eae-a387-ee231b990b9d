import {
  CanActivate,
  ExecutionContext,
  Injectable,
  mixin,
  Type,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export const HeaderAuthGuard = (
  headerKey: string,
  envKeyName: string,
): Type<any> => {
  @Injectable()
  class HeaderAuthGuardMixin implements CanActivate {
    private expectedValue: string;

    constructor(private readonly configService: ConfigService) {
      this.expectedValue = this.configService.getOrThrow<string>(envKeyName);
    }

    canActivate(context: ExecutionContext): boolean {
      const request = context.switchToHttp().getRequest();
      const authHeader = request.get(headerKey);

      if (!authHeader || authHeader !== this.expectedValue) return false;
      return true;
    }
  }

  return mixin(HeaderAuthGuardMixin);
};
