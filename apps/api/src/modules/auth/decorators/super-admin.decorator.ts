import { LoggerFactory } from '@/modules/shared/logger/logger.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  LoggerService,
  SetMetadata,
} from '@nestjs/common';

export const REQUIRE_SUPER_ADMIN_KEY = 'requireSuperAdmin';

export const RequireSuperAdmin = () =>
  SetMetadata(REQUIRE_SUPER_ADMIN_KEY, true);

@Injectable()
export class SuperAdminGuard implements CanActivate {
  private readonly logger: LoggerService;

  constructor(
    private prisma: PrismaService,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(SuperAdminGuard.name);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Since this guard runs after the AuthGuard, user should be populated
    if (!user) {
      this.logger.warn('SuperAdminGuard: User not found in request');
      return false;
    }

    // First check if user has Admin role
    const hasAdminRole = user.role === 'Admin';
    if (!hasAdminRole) {
      throw new ForbiddenException('Admin role required');
    }

    try {
      // Now query the database to check if this admin is a superAdmin
      const adminUser = await this.prisma.user.findFirstOrThrow({
        where: { id: user.userId, type: 'admin' },
        include: { admin: { select: { role: true } } },
      });

      const isSuperAdmin = adminUser.admin?.role === 'superAdmin';
      if (!isSuperAdmin) {
        throw new ForbiddenException('Super admin privileges required');
      }

      return true;
    } catch (error) {
      this.logger.error(
        error,
        { user },
        'SuperAdminGuard: Error fetching admin info:',
      );
      throw new ForbiddenException('Super admin privileges required');
    }
  }
}
