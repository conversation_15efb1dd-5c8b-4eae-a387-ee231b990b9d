import { PrismaService } from '@/modules/prisma/prisma.service';
import {
  AdminAddUserToGroupCommand,
  AdminCreateUserCommand,
  AdminDeleteUserCommand,
  AdminGetUserCommand,
  AdminSetUserPasswordCommand,
  AdminUpdateUserAttributesCommand,
  AdminUserGlobalSignOutCommand,
  CognitoIdentityProviderClient,
  UserNotFoundException,
} from '@aws-sdk/client-cognito-identity-provider';
import { CacheService } from '@modules/cache/cache.service';
import { SesService } from '@modules/shared/aws/ses/ses.service';
import { CognitoError } from '@modules/shared/errors/cognito.error';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as AmazonCognitoIdentity from 'amazon-cognito-identity-js';
import { CognitoUser, ICognitoUserData } from 'amazon-cognito-identity-js';
import { AttributeListType } from 'aws-sdk/clients/cognitoidentityserviceprovider';

import { LoggerFactory, LoggerService } from '../shared/logger/logger.service';
import { roles } from './types/roles';

export type Roles = keyof typeof roles;

type UserPools = {
  [key in Roles]: AmazonCognitoIdentity.CognitoUserPool;
};

/**
 * Handels all communication with cognito
 */
@Injectable()
export class CognitoService {
  private readonly logger: LoggerService;

  private userPools: UserPools;
  private cognitoClient: CognitoIdentityProviderClient;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
    private readonly sesService: SesService,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(CognitoService.name);
    // init cognito
    this.cognitoClient = new CognitoIdentityProviderClient({
      region: configService.get<string>('AWS_REGION'),
      credentials: {
        accessKeyId: configService.get<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: configService.get<string>('AWS_SECRET_ACCESS_KEY'),
      },
    });

    // init user pools for different frontend clients
    this.userPools = {} as UserPools;
    Array.from(Object.keys(roles)).forEach((role) => {
      this.userPools[role] = new AmazonCognitoIdentity.CognitoUserPool({
        ClientId: configService.get<string>(
          `COGNITO_CLIENT_ID_${role.toUpperCase()}`,
        ),
        UserPoolId: configService.get<string>('COGNITO_USER_POOL_ID'),
      });
    });
  }

  /**
   * Authenticates a user
   * @param username the users username - typically an email address
   * @param password the users password
   * @param role the users role
   * @returns The users cognito session if successful
   */
  async signIn(
    username: string,
    password: string,
    role: Roles,
  ): Promise<AmazonCognitoIdentity.CognitoUserSession> {
    const authenticationDetails =
      new AmazonCognitoIdentity.AuthenticationDetails({
        Username: username,
        Password: password,
      });

    const cognitoUser = new AmazonCognitoIdentity.CognitoUser({
      Username: username,
      Pool: this.userPools[role],
    });

    return await new Promise<AmazonCognitoIdentity.CognitoUserSession>(
      (resolve, reject) => {
        cognitoUser.authenticateUser(authenticationDetails, {
          onSuccess: (session) => {
            resolve(session);
          },
          onFailure: (err) => {
            reject(err);
          },
        });
      },
    );
  }

  async signUp(email: string, password: string, role: Roles) {
    try {
      const existingUser = await this.getUser(email);
      if (
        role === 'Patient' &&
        existingUser &&
        ((existingUser['custom:lead'] &&
          existingUser['custom:lead'] == 'true') ||
          (existingUser['custom:referral'] &&
            existingUser['custom:referral'] == 'true'))
      ) {
        const patient = await this.prisma.user.findUnique({
          where: {
            email: email,
          },
        });
        if (!patient) {
          const updatePasswordCmd = new AdminSetUserPasswordCommand({
            UserPoolId: this.configService.get('COGNITO_USER_POOL_ID'),
            Username: email,
            Password: password,
            Permanent: true,
          });
          await this.cognitoClient.send(updatePasswordCmd);
          //add user to group
          const addUserToGroupCmd = new AdminAddUserToGroupCommand({
            UserPoolId: this.configService.get('COGNITO_USER_POOL_ID'),
            Username: email,
            GroupName: role,
          });
          await this.cognitoClient.send(addUserToGroupCmd);
          return existingUser;
        } else {
          throw new CognitoError(
            'User with provided email already exist. Please login in using your existing credentials.',
          );
        }
      } else {
        const createUserCmd = new AdminCreateUserCommand({
          UserPoolId: this.userPools[role].getUserPoolId(),
          Username: email,
          TemporaryPassword: password,
          UserAttributes: [
            { Name: 'email', Value: email },
            { Name: 'email_verified', Value: 'true' },
          ],
          MessageAction: 'SUPPRESS',
        });

        const user = await this.cognitoClient.send(createUserCmd);

        const updatePasswordCmd = new AdminSetUserPasswordCommand({
          UserPoolId: this.configService.get('COGNITO_USER_POOL_ID'),
          Username: email,
          Password: password,
          Permanent: true,
        });
        await this.cognitoClient.send(updatePasswordCmd);

        //add user to group
        const addUserToGroupCmd = new AdminAddUserToGroupCommand({
          UserPoolId: this.configService.get('COGNITO_USER_POOL_ID'),
          Username: email,
          GroupName: role,
        });
        await this.cognitoClient.send(addUserToGroupCmd);

        for (const attribute of user.User.Attributes) {
          user[attribute.Name] = attribute.Value;
        }
        return user;
      }
    } catch (e) {
      throw new CognitoError(e.message);
    }
  }

  async newLead(email: string, password: string, role: Roles) {
    try {
      const createUserCmd = new AdminCreateUserCommand({
        UserPoolId: this.userPools[role].getUserPoolId(),
        Username: email,
        TemporaryPassword: password,
        UserAttributes: [
          { Name: 'email', Value: email },
          { Name: 'email_verified', Value: 'true' },
          { Name: 'custom:lead', Value: 'true' },
        ],
        MessageAction: 'SUPPRESS',
      });

      return await this.cognitoClient.send(createUserCmd);
    } catch (e) {
      if (e.name === 'UsernameExistsException') {
        this.logger.warn('User already exists');
      } else {
        this.logger.error(
          e,
          {
            email,
            role,
          },
          `Error creating new lead: ${e.message}`,
        );
        throw new CognitoError(e.message);
      }
    }
  }

  async newReferralInvite(email: string, password: string, role: Roles) {
    try {
      const existing = await this.getUser(email);

      if (existing)
        return {
          userId: existing.Username,
        };

      const createUserCmd = new AdminCreateUserCommand({
        UserPoolId: this.userPools[role].getUserPoolId(),
        Username: email,
        TemporaryPassword: password,
        UserAttributes: [
          { Name: 'email', Value: email },
          { Name: 'email_verified', Value: 'true' },
          { Name: 'custom:referral', Value: 'true' },
        ],
        MessageAction: 'SUPPRESS',
      });

      const user = await this.cognitoClient.send(createUserCmd);
      return {
        userId: user.User.Username,
      };
    } catch (e) {
      if (e.name === 'UsernameExistsException') {
        this.logger.warn('User already exists', { email, role });
      } else {
        this.logger.error(
          e,
          {
            email,
            role,
          },
          `Error creating new referral invite: ${e.message}`,
        );
        throw new CognitoError(e.message);
      }
    }
  }

  /**
   * Requests a password reset code for a user
   * @param username The email address/username of the user who forgot his password
   * @returns The status of the user request
   */
  async forgotPassword(
    username: string,
  ): Promise<{ message: string; status: number }> {
    return new Promise((resolve) => {
      const userData: ICognitoUserData = {
        Username: username,
        Pool: this.userPools[roles.Patient], // it's not important which client id we are using
      };
      const cognitoUser = new CognitoUser(userData);

      cognitoUser.forgotPassword({
        onSuccess: function (result) {
          resolve({ message: JSON.stringify(result), status: 201 });
        },
        onFailure: function (err) {
          resolve({ message: err.message || JSON.stringify(err), status: 400 });
        },
      });
    });
  }

  /**
   * Generates a custom password reset token and stores it in Redis
   * This bypasses Cognito's email system allowing you to send custom emails
   * @param username The email address/username of the user
   * @param role The user's role
   * @returns A custom reset token and expiration date
   */
  async generateCustomPasswordResetToken(
    username: string,
    role: Roles,
  ): Promise<{ resetToken: string; expiresAt: Date }> {
    try {
      // Verify user exists in Cognito first
      const user = await this.getUser(username);
      if (!user) {
        throw new CognitoError('User not found');
      }

      // Generate a secure random token
      const resetToken = await this.generateSecureToken();
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes

      // Store token data in Redis with 30 minute expiration
      const tokenData = {
        email: username,
        role,
        createdAt: new Date().toISOString(),
      };

      const cacheKey = `password_reset:${resetToken}`;
      await this.cacheService.set(cacheKey, tokenData, 30 * 60); // 30 minutes in seconds

      return { resetToken, expiresAt };
    } catch (error) {
      throw new CognitoError(
        `Failed to generate reset token for user ${username}: ${error.message}`,
      );
    }
  }

  /**
   * Validates a custom reset token from Redis and resets the password
   * @param resetToken The custom reset token
   * @param newPassword The new password
   * @returns Success status with user email
   */
  async resetPasswordWithCustomToken(
    resetToken: string,
    newPassword: string,
  ): Promise<{ message: string; status: number; email?: string }> {
    try {
      // Retrieve token data from Redis
      const cacheKey = `password_reset:${resetToken}`;
      const tokenData = await this.cacheService.get<{
        email: string;
        role: string;
        createdAt: string;
      }>(cacheKey);

      if (!tokenData) {
        return { message: 'Invalid or expired reset token', status: 400 };
      }

      // Verify user still exists in Cognito
      const user = await this.getUser(tokenData.email);
      if (!user) {
        return { message: 'User not found', status: 400 };
      }

      // Reset password in Cognito using admin override
      await this.overridePassword(tokenData.email, newPassword);

      // Remove the used token from Redis
      await this.cacheService.del(cacheKey);

      return {
        message: 'Password reset successfully',
        status: 200,
        email: tokenData.email,
      };
    } catch (error) {
      throw new CognitoError(`Failed to reset password: ${error.message}`);
    }
  }

  /**
   * Sends a password reset email with role-specific template
   * @param email The user's email address
   * @param resetToken The password reset token
   * @param role The user's role
   */
  async sendPasswordResetEmail(
    email: string,
    resetToken: string,
    role: Roles,
  ): Promise<void> {
    try {
      let baseUrl: string;
      let resetUrl: string;

      // Set base URL based on role
      switch (role) {
        case 'Patient':
          baseUrl =
            this.configService.get<string>('PATIENTS_URL') ||
            'https://app.startwillow.com';
          resetUrl = `${baseUrl}/account/reset-password/${resetToken}`;
          break;
        case 'Doctor':
          baseUrl =
            this.configService.get<string>('DOCTORS_URL') ||
            'https://doctors.startwillow.com';
          resetUrl = `${baseUrl}/reset-password/${resetToken}`;
          break;
        default:
          throw new CognitoError(`Unknown user role: ${role}`);
      }

      const subject = 'Willow Reset Password';
      const textBody = `Please click the following link to reset your password: ${resetUrl}`;

      await this.sesService.sendEmail({
        to: email,
        subject,
        textBody,
      });
    } catch (error) {
      throw new CognitoError(
        `Failed to send password reset email: ${error.message}`,
      );
    }
  }

  /**
   * Generates a custom password reset token, stores it, and sends the reset email
   * @param username The email address/username of the user
   * @param role The user's role
   * @returns Success status
   */
  async initiatePasswordReset(
    username: string,
    role: Roles,
  ): Promise<{ message: string; status: number }> {
    try {
      // Generate token and store in Redis
      const { resetToken } = await this.generateCustomPasswordResetToken(
        username,
        role,
      );

      // Send the reset email
      await this.sendPasswordResetEmail(username, resetToken, role);

      return {
        message: 'Password reset email sent successfully',
        status: 200,
      };
    } catch (error) {
      throw new CognitoError(
        `Failed to initiate password reset: ${error.message}`,
      );
    }
  }

  /**
   * Generates a cryptographically secure random token
   * @returns A secure random token (16 characters)
   */
  private async generateSecureToken(): Promise<string> {
    const crypto = await import('crypto');
    return crypto.randomBytes(8).toString('hex'); // 8 bytes = 16 hex characters
  }

  /**
   * Resets the password of a user by a confirmation code.
   * @param username The email address/username of the user
   * @param newPassword The new password
   * @param confirmationCode The confirmation code retrieved by the forgotPassword call
   * @returns
   */
  async resetPassword(
    username: string,
    newPassword: string,
    confirmationCode: string,
  ): Promise<{ message: string; status: number }> {
    return new Promise((resolve) => {
      const userData: ICognitoUserData = {
        Username: username,
        Pool: this.userPools[roles.Patient], // it's not important which client id we are using
      };

      const cognitoUser = new CognitoUser(userData);

      return cognitoUser.confirmPassword(confirmationCode, newPassword, {
        onSuccess: () => {
          resolve({ message: 'Password confirmed', status: 200 });
        },
        onFailure: (err) => {
          resolve({ message: err.message || JSON.stringify(err), status: 400 });
        },
      });
    });
  }

  /**
   *
   * @param refreshToken
   * @param appClient
   * @returns
   */
  async refresh(
    refreshToken: string,
    role: string,
  ): Promise<AmazonCognitoIdentity.CognitoUserSession> {
    const cognitoUser = new AmazonCognitoIdentity.CognitoUser({
      Username: 'username',
      Pool: this.userPools[role],
    });

    const refreshDetails = new AmazonCognitoIdentity.CognitoRefreshToken({
      RefreshToken: refreshToken,
    });

    const result = await new Promise<AmazonCognitoIdentity.CognitoUserSession>(
      (resolve, reject) => {
        cognitoUser.refreshSession(refreshDetails, (err, session) => {
          if (err) {
            reject(err);
          } else {
            resolve(session);
          }
        });
      },
    );
    return result;
  }

  async deleteUser(username: string) {
    try {
      const deleteUserCommand = new AdminDeleteUserCommand({
        UserPoolId: this.configService.get('COGNITO_USER_POOL_ID'),
        Username: username,
      });
      return await this.cognitoClient.send(deleteUserCommand);
    } catch (error) {
      if (error instanceof UserNotFoundException) {
        console.warn(`User ${username} not found in Cognito`);
        return;
      }
      console.error(`Error deleting user ${username} from Cognito:`, error);
      throw error;
    }
  }

  async getUser(email: string) {
    try {
      const params = {
        UserPoolId: this.userPools['Patient'].getUserPoolId(),
        Username: email,
      };

      const adminGetUserCommand = new AdminGetUserCommand(params);
      const data = await this.cognitoClient.send(adminGetUserCommand);
      for (const attribute of data.UserAttributes) {
        data[attribute.Name] = attribute.Value;
      }
      delete data.UserAttributes;
      delete data.$metadata;

      return data;
    } catch (e) {
      return;
    }
  }

  async updateUserEmail(oldEmail: string, newEmail: string): Promise<void> {
    try {
      const params = {
        UserPoolId: this.configService.get('COGNITO_USER_POOL_ID'),
        Username: oldEmail,
        UserAttributes: [
          {
            Name: 'email',
            Value: newEmail,
          },
          {
            Name: 'email_verified',
            Value: 'true',
          },
        ],
      };

      const command = new AdminUpdateUserAttributesCommand(params);
      await this.cognitoClient.send(command);
    } catch (error) {
      throw new CognitoError(
        `Failed to update email for user ${oldEmail}: ${error.message}`,
      );
    }
  }

  async overridePassword(username: string, password: string): Promise<void> {
    try {
      const updatePasswordCmd = new AdminSetUserPasswordCommand({
        UserPoolId: this.configService.get('COGNITO_USER_POOL_ID'),
        Username: username,
        Password: password,
        Permanent: true,
      });

      await this.cognitoClient.send(updatePasswordCmd);
    } catch (error) {
      throw new CognitoError(
        `Failed to override password for user ${username}: ${error.message}`,
      );
    }
  }

  async updateUser(
    username: string,
    role: Roles,
    attributes: AttributeListType,
  ) {
    const params = new AdminUpdateUserAttributesCommand({
      UserAttributes: attributes,
      Username: username,
      UserPoolId: this.userPools[role].getUserPoolId(),
    });
    return await this.cognitoClient.send(params);
  }

  /**
   * Invalidates all tokens for a user by signing them out globally
   * @param username The email address/username of the user to sign out
   */
  async signOutGlobally(username: string): Promise<void> {
    try {
      const command = new AdminUserGlobalSignOutCommand({
        UserPoolId: this.configService.get('COGNITO_USER_POOL_ID'),
        Username: username,
      });
      await this.cognitoClient.send(command);
    } catch (error) {
      if (error instanceof UserNotFoundException) {
        console.warn(
          `User ${username} not found in Cognito during global sign out`,
        );
        return;
      }
      console.error(`Failed to sign out user ${username} globally:`, error);
      throw new CognitoError(
        `Failed to sign out user globally: ${error.message}`,
      );
    }
  }
}
