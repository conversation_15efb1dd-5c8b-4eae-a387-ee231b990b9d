import type {
  IPharmacyService,
  MultiplePrescriptionRequest,
  MultiplePrescriptionResponse,
  PharmacyPatient,
  PharmacyPrescriber,
  PharmacyPrescriptionProduct,
  PrescriptionRequest,
} from '@modules/integrations/pharmacy';
import { CacheService } from '@modules/cache/cache.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { format } from 'date-fns';

interface GetTokenResponse {
  token: string;
  tokenCreatedTime: string;
  tokenExpirationTime: string;
}

interface EasyRxResponse {
  clientOrderId: string;
  eipOrderId: number;
  note: string;
}

interface ApiPatient {
  clientPatientId: string;
  lastName: string;
  firstName: string;
  gender: number;
  dateOfBirth: string;
  address: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    stateProvince: string;
    postalCode: string;
    countryCode: string;
  };
  phoneNumber: string;
  email?: string;
}

interface ApiPrescriber {
  npi: string;
  stateLicenseNumber: string;
  deaNumber?: string;
  lastName: string;
  firstName: string;
  address: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    stateProvince: string;
    postalCode: string;
    countryCode: string;
  };
  phoneNumber: string;
}

interface ApiDiagnosis {
  clinicalInformationQualifier: number;
  primary: {
    code: string;
    qualifier: number;
    description: string;
  };
}

interface ApiMedication {
  itemDesignatorId: string;
  clientPrescriptionId?: string;
  essentialCopy?: string;
  drugDescription: string;
  quantity: string;
  refills: string;
  daysSupply: string;
  writtenDate: string;
  diagnosis: ApiDiagnosis;
  sigText: string;
  note: string;
}

interface ApiNewRx {
  patient: ApiPatient;
  prescriber: ApiPrescriber;
  medication: ApiMedication;
}

interface ApiEasyRxPayload {
  clientOrderId: string;
  poNumber?: string;
  deliveryService: string;
  allowOverrideDeliveryService: boolean;
  allowOverrideEssentialCopyGuidance: boolean;
  newRxs: ApiNewRx[];
  referenceFields?: {
    reference1?: string;
    reference2?: string;
    reference3?: string;
    reference4?: string;
    reference5?: string;
  };
}

interface TokenCacheData {
  token: string;
  expiresAt: string;
  usageCount: number;
}

@Injectable()
export class EmpowerPharmacyService implements IPharmacyService {
  private readonly logger = new Logger(EmpowerPharmacyService.name);
  private readonly httpClient: AxiosInstance;
  private readonly baseUrl: string;

  private readonly TOKEN_CACHE_KEY = 'empower:api:token';
  private readonly MAX_TOKEN_USAGE = 10;

  constructor(
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
    private readonly prisma: PrismaService,
  ) {
    this.baseUrl = this.configService.get<string>('EMPOWER_API_URL');

    this.httpClient = axios.create({ baseURL: this.baseUrl });
  }

  async submitPrescriptions(
    request: MultiplePrescriptionRequest,
  ): Promise<MultiplePrescriptionResponse> {
    if (request.prescriptions.length === 0) {
      return {
        success: true,
        message: 'No prescriptions to submit',
        results: [],
      };
    }

    // For Empower, we bundle all prescriptions into a single EasyRx request
    // We'll use the first prescription for patient/prescriber info (they should all be the same)
    const firstPrescription = request.prescriptions[0];
    const currentDate = format(new Date(), "yyyy-MM-dd'T'00:00:00");

    // Combine all newRxs from all prescriptions
    const allNewRxs: ApiNewRx[] = [];
    for (const prescription of request.prescriptions) {
      const newRxs = prescription.products.map((product) => ({
        patient: this.transformEmpowerPatient(prescription.patient),
        prescriber: this.transformEmpowerPrescriber(prescription.prescriber),
        medication: this.transformEmpowerMedication(
          product,
          currentDate,
          prescription.treatmentId,
        ),
      }));
      allNewRxs.push(...newRxs);
    }

    // Build the combined payload
    // Note: For bulk orders, we use a BULK prefix since we can't use a single treatmentId
    // Add random suffix to ensure uniqueness for multiple requests within same second
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const apiPayload: ApiEasyRxPayload = {
      clientOrderId: `BULK-${format(new Date(), 'yyyyMMddHHmmss')}-${randomSuffix}`,
      poNumber: firstPrescription.poNumber,
      deliveryService:
        firstPrescription.deliveryService || 'UPS Priority 2-Day',
      allowOverrideDeliveryService: true,
      allowOverrideEssentialCopyGuidance: true,
      newRxs: allNewRxs,
      referenceFields: this.transformEmpowerReferenceFields(
        firstPrescription.referenceFields,
      ),
    };

    let responseData: any = null;
    let responseStatus = 500;
    let orderId: string | null = null;

    try {
      const token = await this.getToken();

      const response = await this.httpClient.post<EasyRxResponse>(
        '/newrx/easyrx',
        apiPayload,
        { headers: { Token: token, 'Content-Type': 'application/json' } },
      );

      responseData = response.data;
      responseStatus = response.status || 200;
      orderId = response.data.eipOrderId.toString();

      // Log successful integration (one record per API request)
      await this.logPharmacyIntegration(
        firstPrescription,
        apiPayload,
        responseData,
        responseStatus,
        orderId,
      );

      // All prescriptions in the bundle succeeded
      const results = request.prescriptions.map((prescription) => ({
        treatmentId: prescription.treatmentId,
        prescriptionId: prescription.prescriptionId,
        success: true,
        orderId: prescription.treatmentId,
        pharmacyOrderId: orderId,
        message: response.data.note,
      }));

      return {
        success: true,
        message: `All ${request.prescriptions.length} prescriptions submitted successfully to Empower`,
        results,
        rawResponse: response.data,
      };
    } catch (error) {
      this.logger.error('Failed to submit prescriptions to Empower', error);

      let errorMessage = 'Unknown error';
      if (error.isAxiosError && error.response) {
        responseStatus = error.response.status;
        responseData = error.response.data;

        if (responseStatus === 429) {
          errorMessage = `Rate limit exceeded: ${error.message}`;
        } else if (responseStatus === 401) {
          await this.cacheService.del(this.TOKEN_CACHE_KEY);
          errorMessage = `Authorization error: ${error.message}`;
        } else if (responseStatus >= 500 && responseStatus < 600) {
          errorMessage = `Server error (${responseStatus}): ${error.message}`;
        } else if (responseStatus === 400) {
          errorMessage = error.response.data?.join(', ') || error.message;
        }

        // Log failed integration (one record per API request)
        await this.logPharmacyIntegration(
          firstPrescription,
          apiPayload,
          responseData,
          responseStatus,
          null,
        );
      }

      // All prescriptions in the bundle failed
      const results = request.prescriptions.map((prescription) => ({
        treatmentId: prescription.treatmentId,
        prescriptionId: prescription.prescriptionId,
        success: false,
        message: errorMessage,
        errors: [{ message: errorMessage }],
      }));

      return {
        success: false,
        message: errorMessage,
        results,
        rawResponse: responseData,
      };
    }
  }

  private transformEmpowerPatient(patient: PharmacyPatient): ApiPatient {
    const formattedDateOfBirth = format(
      new Date(patient.dateOfBirth),
      "yyyy-MM-dd'T'00:00:00",
    );
    const genderValue = patient.gender === 'male' ? 0 : 1;

    let phoneNumber = patient.phoneNumber || '**********';
    phoneNumber = phoneNumber.replace(/\D/g, '');

    if (phoneNumber.length > 15) {
      phoneNumber = phoneNumber.substring(0, 15);
    }

    if (phoneNumber.length < 10) {
      phoneNumber = '**********';
    }

    return {
      clientPatientId: patient.id,
      lastName: patient.lastName,
      firstName: patient.firstName,
      gender: genderValue,
      dateOfBirth: formattedDateOfBirth,
      address: {
        addressLine1: patient.address.street1,
        addressLine2: patient.address.street2,
        city: patient.address.city,
        stateProvince: patient.address.state,
        postalCode: patient.address.zipCode,
        countryCode: patient.address.country || 'US',
      },
      phoneNumber,
      email: patient.email,
    };
  }

  private transformEmpowerPrescriber(
    prescriber: PharmacyPrescriber,
  ): ApiPrescriber {
    if (!prescriber.npi) {
      throw new Error('Prescriber NPI number is required');
    }

    const environment = this.configService.get<string>('ENVIRONMENT');
    const isProduction = environment === 'production';

    if (isProduction && !prescriber.stateLicenseNumber) {
      throw new Error(
        'Prescriber state license number is required in production',
      );
    }

    const stateLicenseNumber =
      prescriber.stateLicenseNumber || (isProduction ? '' : 'NO_LICENSE');

    const phoneNumber = '**********';

    return {
      npi: prescriber.npi,
      stateLicenseNumber,
      deaNumber: prescriber.deaNumber,
      lastName: prescriber.lastName,
      firstName: prescriber.firstName,
      address: {
        addressLine1: '1209 Orange Street',
        addressLine2: undefined,
        city: 'Wilmington',
        stateProvince: 'DE',
        postalCode: '19801',
        countryCode: 'US',
      },
      phoneNumber,
    };
  }

  private transformEmpowerMedication(
    product: PharmacyPrescriptionProduct,
    writtenDate: string,
    treatmentId: string,
  ): ApiMedication {
    // Calculate days supply based on quantity (quantity * 28)
    const daysSupply = (product.quantity * 28).toString();

    return {
      itemDesignatorId: product.externalId,
      clientPrescriptionId: `RX-${treatmentId}-${product.id}`,
      drugDescription: product.drugDescription,
      quantity: product.quantity.toString(),
      refills: '0', // Always 0 as per requirements
      daysSupply,
      writtenDate,
      diagnosis: {
        clinicalInformationQualifier: 0,
        primary: {
          code: 'nothing',
          qualifier: 0,
          description: product.note
            ? this.getMedicalNecessityText(product.note, product)
            : 'nothing',
        },
      },
      sigText: product.sig,
      note: product.note || '',
    };
  }

  private transformEmpowerReferenceFields(
    referenceFields?: Record<string, string>,
  ): ApiEasyRxPayload['referenceFields'] {
    if (!referenceFields) {
      return undefined;
    }

    const apiReferenceFields: ApiEasyRxPayload['referenceFields'] = {};
    const fieldNames = [
      'reference1',
      'reference2',
      'reference3',
      'reference4',
      'reference5',
    ];

    Object.entries(referenceFields).forEach(([_, value], index) => {
      if (index < 5) {
        apiReferenceFields[fieldNames[index]] = value;
      }
    });

    return apiReferenceFields;
  }

  private async getToken(): Promise<string> {
    const cachedToken = await this.cacheService.get<TokenCacheData>(
      this.TOKEN_CACHE_KEY,
    );

    if (
      cachedToken &&
      new Date(cachedToken.expiresAt) > new Date() &&
      cachedToken.usageCount < this.MAX_TOKEN_USAGE
    ) {
      this.logger.debug('Using cached token');

      await this.cacheService.set<TokenCacheData>(
        this.TOKEN_CACHE_KEY,
        {
          ...cachedToken,
          usageCount: cachedToken.usageCount + 1,
        },
        3600,
      );

      return cachedToken.token;
    }

    this.logger.debug('Generating new token');
    return this.generateNewToken();
  }

  private async generateNewToken(): Promise<string> {
    try {
      const apiKey = this.configService.get<string>('EMPOWER_API_KEY');
      const apiSecret = this.configService.get<string>('EMPOWER_API_SECRET');

      if (!apiKey || !apiSecret) {
        throw new Error('Missing Empower API credentials');
      }

      const response = await this.httpClient.post<GetTokenResponse>(
        '/gettoken/post',
        {},
        { headers: { APIKey: apiKey, APISecret: apiSecret } },
      );

      const { token, tokenExpirationTime } = response.data;

      const expiresAt = new Date(tokenExpirationTime);
      const now = new Date();
      const ttlSeconds = Math.floor(
        (expiresAt.getTime() - now.getTime()) / 1000,
      );

      await this.cacheService.set<TokenCacheData>(
        this.TOKEN_CACHE_KEY,
        { token, expiresAt: tokenExpirationTime, usageCount: 1 },
        ttlSeconds > 0 ? ttlSeconds : 3600,
      );

      return token;
    } catch (error) {
      this.logger.error('Failed to generate Empower API token', error);
      throw new Error(`Failed to generate Empower API token: ${error.message}`);
    }
  }

  private async logPharmacyIntegration(
    request: PrescriptionRequest,
    apiPayload: any,
    responseData: any,
    responseStatus: number,
    orderId: string | null,
  ): Promise<void> {
    try {
      // Get pharmacy ID from slug
      const pharmacy = await this.prisma.pharmacy.findFirst({
        where: { slug: 'empower' },
      });

      if (!pharmacy) {
        this.logger.warn('Could not find Empower pharmacy record for logging');
        return;
      }

      await this.prisma.pharmacyIntegration.create({
        data: {
          pharmacyId: pharmacy.id,
          prescriptionId: request.prescriptionId,
          orderId: orderId || apiPayload.clientOrderId, // Use clientOrderId as fallback
          request: apiPayload,
          response: responseData || {},
          responseStatus,
        },
      });
    } catch (error) {
      this.logger.error('Failed to log pharmacy integration', error);
      // Don't throw - logging failure shouldn't break the main flow
    }
  }

  getMedicalNecessityText(
    key: string,
    product?: PharmacyPrescriptionProduct,
  ): string {
    if (!key) return '';

    // Create a composite key for product-specific mappings
    const productName = product?.name?.toUpperCase() || '';
    const compositeKey = `${key}_${productName}`;

    const productSpecificMappings: Record<string, string> = {
      // BMI Under 27 mappings
      bmiUnder27_SEMAGLUTIDE:
        'Requires unique dosing due to BMI considerations for Semaglutide therapy',
      bmiUnder27_TIRZEPATIDE:
        'Requires unique dosing due to BMI considerations for Tirzepatide therapy',

      // Lose fat without losing muscle mappings
      loseFatWithoutLosingMuscle_SEMAGLUTIDE:
        'The addition of Cyanocobalamin is needed to maintain muscle mass during Semaglutide treatment',
      loseFatWithoutLosingMuscle_TIRZEPATIDE:
        'The addition of Niacinamide is needed to maintain muscle mass during Tirzepatide treatment',

      // Decrease fatigue increase energy mappings
      decreaseFatigueIncreaseEnergy_SEMAGLUTIDE:
        'The addition of Cyanocobalamin is needed to reduce fatigue and support energy during Semaglutide therapy',
      decreaseFatigueIncreaseEnergy_TIRZEPATIDE:
        'The addition of Niacinamide is needed to reduce fatigue and support energy during Tirzepatide therapy',

      // Support heart health mappings
      supportHeartHealth_SEMAGLUTIDE:
        'The addition of Cyanocobalamin is needed to support cardiovascular health with Semaglutide',
      supportHeartHealth_TIRZEPATIDE:
        'The addition of Niacinamide is needed to support cardiovascular health with Tirzepatide',

      // Improve skin look and feel mappings
      improveSkinLookAndFeel_SEMAGLUTIDE:
        'The addition of Cyanocobalamin is needed to support skin tone and texture during Semaglutide treatment',
      improveSkinLookAndFeel_TIRZEPATIDE:
        'The addition of Niacinamide is needed to support skin tone and texture during Tirzepatide treatment',

      // Dosing concerns mappings
      dosingConcerns_SEMAGLUTIDE:
        'Requires tailored dosing to ensure accurate titration for Semaglutide',
      dosingConcerns_TIRZEPATIDE:
        'Requires tailored dosing to ensure accurate titration for Tirzepatide',
    };

    return productSpecificMappings[compositeKey] || '';
  }
}
