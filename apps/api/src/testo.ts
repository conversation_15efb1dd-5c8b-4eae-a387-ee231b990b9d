import { createWriteStream } from 'fs';
import { NestFactory } from '@nestjs/core';

import { AppModule } from './app.module';
import { PatientGetHsaReceiptUseCase } from './modules/patient/use-cases/patient-get-hsa-receipt.use-case';
import { PatientGetLetterOfMedicalNecessityUseCase } from './modules/patient/use-cases/patient-get-letter-of-medical-necesity.use-case';
import { PrismaService } from './modules/prisma/prisma.service';

//process.env.DATABASE_URL =
//  'postgres://user:password@127.0.0.1:5432/willow-prod';
process.env.DATABASE_URL =
  '*****************************************************************************************************************************************************/willow';

process.env.IS_CLI = 'true';
process.env.ENABLE_PGBOSS_CONSUMER = 'false';
process.env.ENABLE_SUBSCRIPTION_WORKERS = 'false';
process.env.ENABLE_SNS_CONSUMER = 'false';
process.env.ENABLE_SQS_CONSUMER = 'false';
process.env.STRIPE_SECRET_KEY =
  '***********************************************************************************************************';

async function testoMedicalNecesityLetter() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const patientGetLetterOfMedicalNecessityUseCase = app.get(
    PatientGetLetterOfMedicalNecessityUseCase,
  );

  const prisma = app.get(PrismaService);
  const patient = await prisma.patient.findFirst({
    where: {
      user: {
        email: '<EMAIL>',
      },
    },
  });
  const document = await patientGetLetterOfMedicalNecessityUseCase.execute(
    patient.userId,
  );
  const writeStream = createWriteStream('hsa-receipt.pdf');
  document.getStream().pipe(writeStream);
  writeStream.on('finish', () => {
    process.exit(0);
  });
}

async function testoHSA() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const patientGetHsaReceiptUseCase = app.get(PatientGetHsaReceiptUseCase);

  const prisma = app.get(PrismaService);
  const patient = await prisma.patient.findFirst({
    where: {
      user: {
        email: '<EMAIL>',
      },
    },
  });
  const document = await patientGetHsaReceiptUseCase.execute(
    patient.userId,
    '2025',
  );
  const writeStream = createWriteStream('hsa-receipt.pdf');
  document.getStream().pipe(writeStream);
  writeStream.on('finish', () => {
    process.exit(0);
  });
}

testoHSA();
