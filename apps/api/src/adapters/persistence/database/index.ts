import { PatientPaymentMethodPersistence } from '@adapters/persistence/database/patient-payment-method.persistence';
import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import { ProductPersistence } from '@adapters/persistence/database/product.persistence';
import { QuestionnairePersistence } from '@adapters/persistence/database/questionnaire.persistence';
import { ShippingAddressPersistence } from '@adapters/persistence/database/shipping-address.persistence';
import { StatePersistence } from '@adapters/persistence/database/state.persistence';

export const OnboardingPersistence = [
  PatientPersistence,
  ProductPersistence,
  PatientPaymentMethodPersistence,
  QuestionnairePersistence,
  ShippingAddressPersistence,
  StatePersistence,
];
