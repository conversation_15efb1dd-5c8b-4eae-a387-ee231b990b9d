'use client';

import { useParams } from 'next/navigation';

import { cn } from '@willow/ui';

import type { DoctorAdminConversationGroups } from '~/types/doctor-admin-chat';
import { NavbarItem, NavbarSection } from '~/components/NavbarItem';
import { useGetDoctorAdminConversationsCounts } from '~/hooks/doctor-admin-chat';

export const doctorAdminConversationGroups: DoctorAdminConversationGroups[] = [
  'myInbox',
  'mySent',
  'myUnreplied',
  'allSent',
  'allUnreplied',
  'all',
  'unassigned',
  'closed',
];

const sections: {
  name: string;
  group?: DoctorAdminConversationGroups;
  subGroups: DoctorAdminConversationGroups[];
}[] = [
  {
    name: 'My Inbox',
    group: 'myInbox',
    subGroups: ['myUnreplied', 'mySent'],
  },
  {
    name: 'All',
    group: 'all',
    subGroups: ['allUnreplied', 'allSent', 'unassigned'],
  },
  {
    name: '',
    subGroups: ['closed'],
  },
] as const;

export const formatCamelCaseText = (str: string): string => {
  if (!str) return '';
  const firstLetterCapitalized = str.charAt(0).toUpperCase() + str.slice(1);
  return firstLetterCapitalized.replace(/([A-Z])/g, ' $1');
};

export const DoctorMessagesSidebar = () => {
  const params = useParams();
  const selectedGroup = params.group;
  const { data: groupCounts, isLoading } =
    useGetDoctorAdminConversationsCounts();

  return (
    <aside className="scrollbar-hidden h-screen w-[284px] overflow-y-scroll bg-stone-light px-6 py-10">
      <div className="flex flex-col gap-6 text-[13px]">
        <div className="flex justify-between text-denim">
          <h1 className="text-2xl">Messages</h1>
        </div>

        {sections.map((section) => (
          <NavbarSection
            href={section.group ? `/messages/${section.group}` : undefined}
            titleCount={
              section.group ? groupCounts?.[section.group] : undefined
            }
            key={section.name}
            title={section.name}
          >
            {section.subGroups.map((group) => (
              <DoctorMessagesSidebarLink
                key={group}
                title={formatCamelCaseText(group)}
                group={group}
                count={groupCounts?.[group]}
                isSelected={selectedGroup === group}
                isLoading={isLoading}
              />
            ))}
          </NavbarSection>
        ))}
      </div>
    </aside>
  );
};

const DoctorMessagesSidebarLink = ({
  title,
  group,
  count = 0,
  isSelected,
  isLoading,
}: {
  title: string;
  group: DoctorAdminConversationGroups;
  count?: number;
  isSelected: boolean;
  isLoading: boolean;
}) => {
  return (
    <NavbarItem
      href={`/messages/${group}`}
      title={title}
      count={count}
      className={cn('text-xs font-normal text-black', {
        'text-denim': isSelected,
      })}
      countClassName={cn('text-xs font-normal text-black', {
        'text-white': isSelected,
        'animate-pulse': isLoading,
        'text-gray-400': count === 0,
      })}
    />
  );
};
