'use client';

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns-tz';
import { XIcon } from 'lucide-react';
import { useQueryState } from 'nuqs';

import { cn } from '@willow/ui';
import { Avatar, AvatarFallback, AvatarImage } from '@willow/ui/base/avatar';
import { Banner } from '@willow/ui/base/banner';
import { Button } from '@willow/ui/base/button';
import { DrawerTitle } from '@willow/ui/base/drawer';
import {
  Drawer,
  DrawerContent,
  DrawerOverlay,
  DrawerPortal,
  DrawerTrigger,
} from '@willow/ui/base/vaul';
import { formatDate } from '@willow/utils/format';

import type {
  DoctorAdminConversationGroups,
  DoctorAdminConversation as DoctorAdminConversationType,
} from '~/types/doctor-admin-chat';
import {
  useAssignDoctorAdminConversation,
  useCloseDoctorAdminConversation,
  useGetDoctorAdminConversations,
  useUnassignDoctorAdminConversation,
} from '~/hooks/doctor-admin-chat';
import { usePatientAssetLink } from '~/hooks/links';
import { useProfile } from '~/hooks/useProfile';
import { PatientInfo } from '../../patients/_components/PatientInfo';
import { DoctorAdminConversationWithRedirect } from './DoctorAdminConversationWithRedirect';
import { formatCamelCaseText } from './DoctorMessagesSidebar';

export const DoctorMessages = ({
  group,
}: {
  group: DoctorAdminConversationGroups;
}) => {
  const [selectedConversationId, setSelectedConversationId] = useQueryState(
    'conversationId',
    {
      defaultValue: '',
    },
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [allConversations, setAllConversations] = useState<
    DoctorAdminConversationType[]
  >([]);
  const [hasMore, setHasMore] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef<number>(0);
  const limit = 20;

  useEffect(() => {
    const handleKeyDown = (event: { key: string }) => {
      if (event.key === 'Escape') {
        setSelectedConversationId('');
      }
    };
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [setSelectedConversationId]);

  useEffect(() => {
    setCurrentPage(1);
    setAllConversations([]);
  }, [group]);

  // Restore scroll position after new conversations are loaded
  useEffect(() => {
    if (scrollContainerRef.current && scrollPositionRef.current > 0) {
      scrollContainerRef.current.scrollTop = scrollPositionRef.current;
      scrollPositionRef.current = 0; // Reset after restoring
    }
  }, [allConversations]);

  const { data: conversationsData, isLoading } = useGetDoctorAdminConversations(
    group,
    currentPage,
    limit,
  );

  useEffect(() => {
    if (conversationsData) {
      if (currentPage === 1) {
        setAllConversations(conversationsData.conversation);
      } else {
        setAllConversations((prev) => [
          ...prev,
          ...conversationsData.conversation,
        ]);
      }
      setHasMore(currentPage < conversationsData.pagination.totalPages);
    }
  }, [conversationsData, currentPage]);

  const selectedConversation = useMemo(() => {
    return allConversations.find((c) => c.id === selectedConversationId);
  }, [allConversations, selectedConversationId]);

  // Memoize the conversation click handler to prevent unnecessary re-renders
  const handleConversationClick = useCallback(
    (conversationId: string) => {
      setSelectedConversationId(conversationId);
    },
    [setSelectedConversationId],
  );

  const handleLoadMore = () => {
    // Capture current scroll position before loading more
    if (scrollContainerRef.current) {
      scrollPositionRef.current = scrollContainerRef.current.scrollTop;
    }
    setCurrentPage((prev) => prev + 1);
  };

  if (isLoading && currentPage === 1) return <div>Loading ...</div>;

  if (!conversationsData) return <div>no Data</div>;

  if (allConversations.length === 0 && !isLoading)
    return (
      <div className="flex h-full items-center justify-center text-gray-500">
        No conversations have been created yet.
      </div>
    );

  return (
    <div className="flex h-full flex-row overflow-scroll">
      <div className="w-1/4">
        <div className="mb-2 px-2 text-2xl font-medium text-slate-600">
          {formatCamelCaseText(group)}
        </div>
        <div
          ref={scrollContainerRef}
          className="h-[calc(100%-52px)] w-full overflow-y-auto"
        >
          {allConversations.map((conversation) => (
            <Conversation
              key={conversation.id}
              conversation={conversation}
              isSelected={conversation.id === selectedConversationId}
              onClickConversation={() =>
                handleConversationClick(conversation.id)
              }
            />
          ))}
          {hasMore && (
            <div className="w-full p-2">
              <Button
                onClick={handleLoadMore}
                variant="denimOutline"
                size="sm"
                className="h-10 w-full text-sm font-semibold"
                loading={isLoading && currentPage > 1}
              >
                Load More
              </Button>
            </div>
          )}
        </div>
      </div>
      <div className="h-full w-3/4 border-l-[0.5px] border-slate-200">
        {selectedConversationId && !!selectedConversation ? (
          <>
            <AssignPatientBanner
              conversation={selectedConversation}
              group={group}
              allConversations={allConversations}
              onConversationChange={handleConversationClick}
            />
            <div
              className={`h-full ${selectedConversation.status === 'closed' ? 'pt-20' : 'pt-12'}`}
            >
              <DoctorAdminConversationWithRedirect
                conversationId={selectedConversationId}
                group={group}
              />
            </div>
          </>
        ) : (
          <div className="flex h-full flex-col items-center justify-center">
            <div className="text-md text-center text-gray-500">
              No conversation selected.
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const Conversation = React.memo(
  ({
    isSelected,
    onClickConversation,
    conversation,
  }: {
    isSelected: boolean;
    conversation: DoctorAdminConversationType;
    onClickConversation: () => void;
  }) => {
    const { genAssetLink } = usePatientAssetLink();

    return (
      <div
        onClick={onClickConversation}
        className={cn(
          'flex cursor-pointer flex-col gap-4 border-[0.5px] border-slate-200 bg-white px-3 py-2',
          {
            'bg-stone-light': isSelected,
          },
        )}
      >
        <div className="flex flex-row items-center gap-4">
          <Avatar className="h-10 w-10">
            <AvatarImage src={genAssetLink(conversation.patient.facePhoto)} />
            <AvatarFallback className="font-bold uppercase text-denim-light">
              {conversation.patient.user.firstName[0]}
              {conversation.patient.user.lastName[0]}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col gap-2">
            <div className="text-xs font-medium text-slate-600">
              {`${conversation.patient.user.firstName} ${conversation.patient.user.lastName}`}
            </div>
            <div className="text-xs font-normal text-zinc-900">
              {`${conversation.patient.doctor.user.firstName} ${conversation.patient.doctor.user.lastName}`}
            </div>
          </div>
        </div>
        <div className="flex flex-row items-end gap-4">
          <div className="line-clamp-2 flex-1 text-xs font-normal text-zinc-900">
            {conversation.lastMessageText}
          </div>
          <div className="text-xs font-normal text-zinc-500">
            {conversation.lastMessage?.createdAt
              ? format(
                  new Date(conversation.lastMessage.createdAt),
                  'MMMM dd, haaa',
                )
              : ''}
          </div>
        </div>
      </div>
    );
  },
);

const AssignPatientBanner = ({
  conversation,
  group,
  allConversations,
}: {
  conversation: DoctorAdminConversationType;
  group: DoctorAdminConversationGroups;
  allConversations: DoctorAdminConversationType[];
  onConversationChange: (conversationId: string) => void;
}) => {
  const { genAssetLink } = usePatientAssetLink();
  const router = useRouter();
  const profile = useProfile();
  const isAssignedAdmin = !!conversation.assignedAdmin;
  const isClosedConversation = conversation.status === 'closed';
  const [selectedPatientId, setSelectedPatientId] = useQueryState('patientId', {
    defaultValue: '',
  });

  const { mutateAsync: unassignAdmin, isPending: unassignAdminPending } =
    useUnassignDoctorAdminConversation();
  const { mutateAsync: assignAdmin, isPending: assignAdminPending } =
    useAssignDoctorAdminConversation();

  const {
    mutateAsync: closeConversation,
    isPending: closeConversationPending,
  } = useCloseDoctorAdminConversation();

  // Helper function to find the next conversation to select
  const findNextConversation = (
    currentId: string,
    conversations: DoctorAdminConversationType[],
  ): string | null => {
    const currentIndex = conversations.findIndex((c) => c.id === currentId);

    // Try next item
    if (currentIndex >= 0 && currentIndex < conversations.length - 1) {
      const nextConversation = conversations[currentIndex + 1];
      return nextConversation ? nextConversation.id : null;
    }

    // Try previous item
    if (currentIndex > 0) {
      const prevConversation = conversations[currentIndex - 1];
      return prevConversation ? prevConversation.id : null;
    }

    // No items available
    return null;
  };

  async function handleToggleAssignAdmin(
    conversationId: string,
    adminUserId: string,
    isAssignedAdmin: boolean,
  ) {
    if (isAssignedAdmin) {
      const nextConversationId = findNextConversation(
        conversationId,
        allConversations,
      );
      await unassignAdmin(conversationId, {
        onSuccess: () => {
          // Stay in current section when unassigning
          const url = nextConversationId
            ? `/messages/${group}?conversationId=${nextConversationId}`
            : `/messages/${group}`;
          router.push(url);
        },
      });
    } else
      await assignAdmin(
        {
          conversationId,
          adminUserId,
        },
        {
          onSuccess: () => {
            router.push(`/messages/myInbox?conversationId=${conversationId}`);
          },
        },
      );
  }

  async function handleCloseConversation(conversationId: string) {
    const nextConversationId = findNextConversation(
      conversationId,
      allConversations,
    );
    await closeConversation(conversationId, {
      onSuccess: () => {
        // Stay in current section when closing
        const url = nextConversationId
          ? `/messages/${group}?conversationId=${nextConversationId}`
          : `/messages/${group}`;
        router.push(url);
      },
    });
  }

  return (
    <div className="overflow-y-scroll">
      <Drawer
        direction="right"
        open={selectedPatientId !== ''}
        onOpenChange={(value) => {
          if (!value) void setSelectedPatientId('');
        }}
      >
        <div className="absolute top-0 z-10 flex w-3/4 flex-col">
          <div className="flex flex-row items-center justify-between bg-stone-light py-5 pl-4 pr-8">
            <DrawerTrigger
              asChild
              onClick={() => {
                void setSelectedPatientId(conversation.patientId);
              }}
            >
              <div className="flex flex-row items-center gap-4">
                <Avatar className="h-10 w-10 cursor-pointer">
                  <AvatarImage
                    src={genAssetLink(conversation.patient.facePhoto)}
                  />
                  <AvatarFallback className="font-bold uppercase text-denim-light">
                    {conversation.patient.user.firstName[0]}
                    {conversation.patient.user.lastName[0]}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col gap-2">
                  <div className="cursor-pointer text-xs font-medium text-slate-600">
                    {`${conversation.patient.user.firstName} ${conversation.patient.user.lastName}`}
                  </div>
                  <div
                    className="text-xs font-normal text-zinc-900"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {`${conversation.patient.doctor.user.firstName} ${conversation.patient.doctor.user.lastName}`}
                  </div>
                </div>
              </div>
            </DrawerTrigger>

            <div className="flex flex-row items-center gap-2">
              {!isClosedConversation && (
                <Button
                  size="sm"
                  variant="denim"
                  className="h-8 text-xs font-semibold text-white"
                  onClick={() =>
                    handleToggleAssignAdmin(
                      conversation.id,
                      profile.id,
                      isAssignedAdmin,
                    )
                  }
                  loading={unassignAdminPending || assignAdminPending}
                >
                  {isAssignedAdmin ? 'UNASSIGN' : 'ASSIGN'}
                </Button>
              )}

              {!isClosedConversation && (
                <Button
                  size="sm"
                  variant="denimOutline"
                  className="h-8 text-xs font-semibold"
                  onClick={() => handleCloseConversation(conversation.id)}
                  loading={closeConversationPending}
                >
                  CLOSE
                  <XIcon className="!h-4 !w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
        {selectedPatientId && (
          <DrawerPortal>
            <DrawerOverlay className="fixed inset-0 z-20 bg-dark/40" />
            <DrawerContent
              className="fixed bottom-0 right-0 top-0 z-50 flex w-[1000px] !touch-none !select-text"
              draggable={false}
              data-vaul-no-drag={true}
            >
              <DrawerTitle className="hidden">Patient Information</DrawerTitle>
              <PatientInfo
                patientId={selectedPatientId}
                handleClose={() => setSelectedPatientId('')}
              />
            </DrawerContent>
          </DrawerPortal>
        )}
      </Drawer>
    </div>
  );
};
