import { format } from 'date-fns';

import type { TreatmentLogDetails } from '@willow/db/client';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@willow/ui/base/table';
import { Loader } from '@willow/ui/loader';

import { useGetPatientTreatmentsLogs } from '~/hooks/treatments';

export function PrescriptionTableLogs({
  treatmentId,
}: {
  treatmentId: string;
}) {
  const {
    data: logs,
    isLoading,
    error,
  } = useGetPatientTreatmentsLogs(treatmentId);

  if (isLoading) return <Loader className="h-full py-32" />;

  if (error)
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <p className="text-red-500">Failed to load prescription history</p>
      </div>
    );

  if (!logs) return null;

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="text-[13px] font-bold text-dark">
            Timestamp
          </TableHead>
          <TableHead className="text-[13px] font-bold text-dark">
            Action
          </TableHead>
          <TableHead className="text-[13px] font-bold text-dark">
            User
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {logs.map((log) => (
          <TableRow key={log.id}>
            <TableCell className="text-[13px] font-medium text-dark">
              {format(new Date(log.createdAt), 'MM/dd/yyyy h:mm a')}
            </TableCell>
            <TableCell className="text-[13px] font-medium text-dark">
              {formatAction(log)}
            </TableCell>
            <TableCell className="text-[13px] font-medium text-dark">
              {formatUser(log.actorType, log.actorId, log.actorExtraDetails)}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
const formatAction = (log: TreatmentLogDetails): string => {
  const action = log.action;
  const details: any = log.details;
  let detailMessage = '';
  if (action === 'TREATMENT_PAUSED' && 'until' in details) {
    if (details.until === null) {
      detailMessage = ' (Indefinite)';
    } else if (details.until) {
      detailMessage = ` (${format(new Date(details.until as string), 'MM/dd/yyyy')})`;
    }
  } else if (action == 'TREATMENT_CREATED' && details?.isTransfer) {
    detailMessage = ' (Transfer)';
  }

  return (
    action
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ') + detailMessage
  );
};

const formatUser = (
  actorType: string,
  actorId: string,
  actorExtraDetails?: { firstName?: string; lastName?: string },
): string => {
  if (actorType === 'SYSTEM') {
    return `Automated - ${actorId.charAt(0).toUpperCase() + actorId.slice(1).toLowerCase()}`;
  }

  if (!actorExtraDetails?.firstName && !actorExtraDetails?.lastName) {
    return actorType
      ? actorType.charAt(0).toUpperCase() + actorType.slice(1).toLowerCase()
      : '_';
  }

  const prefix = !actorType
    ? '_'
    : actorType === 'DOCTOR'
      ? 'Dr.'
      : actorType.charAt(0).toUpperCase() + actorType.slice(1).toLowerCase();

  return `${prefix} ${actorExtraDetails.firstName || ''} ${actorExtraDetails.lastName || ''}`.trim();
};
