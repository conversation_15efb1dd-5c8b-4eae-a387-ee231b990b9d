import { useEffect } from 'react';

import { AdminPatientById } from '@willow/db/client';
import { useInvalidatedQuery } from '@willow/utils/react-query';

import {
  CreateConversation,
  DoctorAdminConversation,
} from './DoctorAdminConversation';

export function DoctorMessagingTab({ patient }: { patient: AdminPatientById }) {
  const invalidate = useInvalidatedQuery();
  useEffect(() => {
    invalidate(['patients', patient.id]);
  }, [invalidate, patient.id]);

  if (patient.doctorAdminConversation?.id) {
    return (
      <DoctorAdminConversation
        conversationId={patient.doctorAdminConversation.id}
      />
    );
  }
  if (patient.doctor) {
    return <CreateConversation patientId={patient.id} />;
  }
  return (
    <div className="flex h-full items-center justify-center">
      <p className="text-muted-foreground">Patient has no assigned doctor</p>
    </div>
  );
}
