import { useMutation, useQuery } from '@tanstack/react-query';

import type { Conversation } from '@willow/chat';
import { apiClient } from '@willow/utils/api/client';
import { useInvalidatedQuery } from '@willow/utils/react-query';

import type {
  DoctorAdminConversationGroups,
  DoctorAdminConversationsResponse,
} from '~/types/doctor-admin-chat';

// Hook to get admin-doctor conversations
export function useGetDoctorAdminConversations(
  filter: DoctorAdminConversationGroups = 'all',
  page = 1,
  limit = 20,
) {
  return useQuery<DoctorAdminConversationsResponse>({
    queryKey: ['admin-doctor-conversations', filter, page, limit],
    queryFn: async () => {
      const response = await apiClient.get<DoctorAdminConversationsResponse>(
        '/doctor-admin-chat/conversations',
        {
          params: { filter, page, limit },
        },
      );
      return response.data;
    },
  });
}

// Hook to get a specific admin-doctor conversation with messages
export function useGetDoctorAdminConversation(
  conversationId: string,
  options?: { enabled?: boolean },
) {
  return useQuery<Conversation>({
    queryKey: ['admin-doctor-conversation', conversationId],
    queryFn: async () => {
      const response = await apiClient.get<Conversation>(
        `/doctor-admin-chat/conversations/${conversationId}`,
      );
      return response.data;
    },
    enabled:
      options?.enabled !== undefined ? options.enabled : !!conversationId,
  });
}

interface UploadDoctorAdminFileParams {
  conversationId: string;
  filename: string;
  file: Blob;
}
// Hook to upload file in admin-doctor conversation
export function useUploadDoctorAdminFile() {
  return useMutation({
    mutationFn: async (params: UploadDoctorAdminFileParams) => {
      interface PreSignedUrlResponse {
        key: string;
        PUTPreSignedURL: string;
      }

      // Get pre-signed URL
      const preSignedUrl = await apiClient
        .get<PreSignedUrlResponse>(
          `/doctor-admin-chat/conversations/${params.conversationId}/get-upload-url/${params.filename}`,
        )
        .then((res) => res.data);

      // Upload file
      await apiClient.put(preSignedUrl.PUTPreSignedURL, params.file, {
        bypassInterceptor: true,
        headers: {
          'Content-Type': params.file.type,
        },
      });

      return preSignedUrl.key;
    },
  });
}

// Hook to create admin-doctor conversation
export function useCreateDoctorAdminConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (params: { patientId: string }) => {
      const response = await apiClient.post<{ conversationId: string }>(
        '/doctor-admin-chat/conversations',
        params,
      );
      return response.data;
    },
    onSuccess: (_, params) => {
      void invalidatedQuery(['admin-doctor-conversations']);
      void invalidatedQuery(['patients', params.patientId]);
    },
  });
}

// Hook to mark conversation as read
export function useMarkDoctorConvAdminersationAsRead() {
  return useMutation({
    mutationFn: async (conversationId: string) => {
      await apiClient.post(
        `/doctor-admin-chat/conversations/${conversationId}/read`,
      );
    },
  });
}

// Hook to assign conversation to admin
export function useAssignDoctorAdminConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async ({
      conversationId,
      adminUserId,
    }: {
      conversationId: string;
      adminUserId?: string;
    }) => {
      const response = await apiClient.put(
        `/doctor-admin-chat/conversations/${conversationId}/assign`,
        { adminUserId },
      );
      return response.data;
    },
    onSuccess: (_, params) => {
      void invalidatedQuery(['admin-doctor-conversations']);
      void invalidatedQuery(['doctorAdmin', params.conversationId]);
    },
  });
}

// Hook to unassign conversation
export function useUnassignDoctorAdminConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (conversationId: string) => {
      const response = await apiClient.put(
        `/doctor-admin-chat/conversations/${conversationId}/unassign`,
      );
      return response.data;
    },
    onSuccess: (_, conversationId) => {
      void invalidatedQuery(['admin-doctor-conversations']);
      void invalidatedQuery(['doctorAdmin', conversationId]);
    },
  });
}

// Hook to close conversation
export function useCloseDoctorAdminConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (conversationId: string) => {
      const response = await apiClient.put(
        `/doctor-admin-chat/conversations/${conversationId}/close`,
      );
      return response.data;
    },
    onSuccess: (_, conversationId) => {
      void invalidatedQuery(['admin-doctor-conversations']);
      void invalidatedQuery(['doctorAdmin', conversationId]);
    },
  });
}

export function useGetDoctorAdminConversationsCounts() {
  return useQuery({
    queryKey: ['admin-doctor-conversations', 'group-counts'],
    queryFn: async () => {
      const response = await apiClient.get(
        '/doctor-admin-chat/conversations/group-counts',
      );
      return response.data as {
        myInbox: number;
        all: number;
        unassigned: number;
        allSent: number;
        allUnreplied: number;
        myUnreplied: number;
        mySent: number;
        closed: number;
      };
    },
  });
}
