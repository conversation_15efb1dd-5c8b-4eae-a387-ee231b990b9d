export interface DoctorAdminConversation {
  id: string;
  patientId: string;
  status: string;
  assignedAdminId: string | null;
  lastMessageText: string | null;
  createdAt: string;
  updatedAt: string;
  closedAt: string | null;
  patient: {
    id: string;
    facePhoto: string;
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    doctor: {
      id: string;
      user: {
        id: string;
        firstName: string;
        lastName: string;
      };
    };
  };
  assignedAdmin: {
    id: string;
    firstName: string;
    lastName: string;
  } | null;
  lastMessage: {
    id: string;
    content: string;
    createdAt: string;
    user: {
      id: string;
      firstName: string;
      lastName: string;
      type: string;
    } | null;
  } | null;
  unreadMessages: number;
}

export interface DoctorAdminConversationsResponse {
  conversation: DoctorAdminConversation[];
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
    pages: number;
  };
}

export type DoctorAdminConversationGroups =
  | 'myInbox' // assigned to current admin
  | 'mySent' // sent by current admin
  | 'myUnreplied' // archived by current admin
  | 'allSent'
  | 'allUnreplied'
  | 'all' // all open conversations
  | 'unassigned' // unassigned open conversations
  | 'closed'; // conversations closed in last 7 days
