import { fileURLToPath } from 'node:url';
import { createJiti } from 'jiti';

// Import env files to validate at build time. Use jiti so we can load .ts files in here.
await createJiti(fileURLToPath(import.meta.url)).import('./src/env');

/** @type {import('next').NextConfig} */
const config = {
  reactStrictMode: true,

  /** Enables hot reloading for local packages without a build step */
  transpilePackages: [
    '@willow/auth',
    '@willow/db',
    '@willow/ui',
    '@willow/utils',
  ],

  /** We already do linting and typechecking as separate tasks in CI */
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'data.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'local-products.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'local-patients.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'local-data.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'staging-products.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'staging-patients.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'staging-data.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'production-products.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'production-patients.startwillow.com',
      },
      {
        protocol: 'https',
        hostname: 'local-products-patients.startwillow.com',
      },
    ],
  },
  webpack(config) {
    const fileLoaderRule = config.module.rules.find(
      /** @param {any} rule */ (rule) => rule.test?.test?.('.svg'),
    );

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ['@svgr/webpack'],
      },
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

export default config;
