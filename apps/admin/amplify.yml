version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - npm install -g pnpm
            - pnpm install
            - pnpm -F @willow/db generate
        build:
          commands:
            - pnpm turbo build --filter=@willow/admin
      artifacts:
        baseDirectory: apps/admin/.next
        files:
          - '**/*'
      cache:
        paths:
          - .next/cache/**/*
          - node_modules/**/*
      buildPath: /
    appRoot: apps/admin
