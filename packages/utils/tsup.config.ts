import { defineConfig } from 'tsup';

export default defineConfig({
  // Entry points - build all TypeScript files
  entry: ['src/**/*.ts', '!src/**/*.spec.ts', '!src/**/*.test.ts'],

  // Build formats
  format: ['cjs', 'esm'],

  // Output file extensions
  outExtension({ format }) {
    return {
      js: format === 'cjs' ? '.js' : '.mjs',
    };
  },

  // Generate .d.ts files
  dts: true,

  // Generate sourcemaps
  sourcemap: true,

  // Clean output directory before build
  clean: true,

  // Split into chunks by default
  splitting: false,

  // Don't bundle these dependencies
  external: [
    '@willow/db',
    '@tanstack/react-query',
    'next',
    'react',
    'react-dom',
  ],

  // Define platform - neutral to support both browser and node
  platform: 'neutral',
});
