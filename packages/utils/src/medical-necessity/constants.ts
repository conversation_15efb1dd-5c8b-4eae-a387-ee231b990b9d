/**
 * Medical necessity mappings for onboarding objectives
 */
export const MEDICAL_NECESSITY_MAPPINGS = {
  loseFatWithoutLosingMuscle: {
    text: 'I want to lose fat without losing muscle',
    priority: 2,
  },
  decreaseFatigueIncreaseEnergy: {
    text: 'I want to decrease fatigue and increase my energy',
    priority: 2,
  },
  supportHeartHealth: {
    text: "I'm interested in supporting my heart health",
    priority: 2,
  },
  improveSkinLookAndFeel: {
    text: "I'd like to improve the look and feel of my skin",
    priority: 2,
  },
  dosingConcerns: {
    text: "I'm concerned about dosing correctly",
    priority: 2,
  },
  noRefrigerationNeeded: {
    text: "I need medication that doesn't require refrigeration",
    priority: 2,
  },
  travelFriendly: {
    text: "I'd like something travel-friendly",
    priority: 2,
  },
  injectionConcerns: {
    text: 'I am concerned about injecting myself',
    priority: 2,
  },
  bmiUnder27: {
    text: 'BMI under 27',
    priority: 1,
  },
} as const;

export type ObjectiveId = keyof typeof MEDICAL_NECESSITY_MAPPINGS;

export type MedicalNecessityMapping =
  (typeof MEDICAL_NECESSITY_MAPPINGS)[ObjectiveId];

/**
 * Medical necessities that indicate preference for oral products
 */
export const ORAL_SPECIFIC_NECESSITIES: ObjectiveId[] = [
  'dosingConcerns',
  'noRefrigerationNeeded',
  'travelFriendly',
  'injectionConcerns',
];

/**
 * Medical necessities that are always system-generated and should never be shown in doctor's edit interface
 */
export const SYSTEM_ONLY_NECESSITIES: ObjectiveId[] = ['bmiUnder27'];
