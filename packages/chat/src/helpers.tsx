export const convertUrlsToLinks = (text: string) => {
  const urlRegex =
    /(https?:\/\/[^\s]+|www\.[^\s]+|[^\s]+\.(com|org|net|edu|gov|io|co|me|us|uk|ca|de|fr|it|es|in|au|br|cn|jp|ru|za|mx|nl|se|no|dk|fi|pl|tr|gr|cz|sk|hu|bg|ro|hr|rs|si|ee|lv|lt|mt|cy|lu|ad|sm|va|mc|li|ch|at|be|pt|ie|is|fo|gl)[^\s]*)/gi;
  const parts: (string | JSX.Element)[] = [];
  let lastIndex = 0;
  let match;
  while ((match = urlRegex.exec(text)) !== null) {
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }
    const url = match[0];

    let href = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      href = 'https://' + url;
    }

    parts.push(
      <a
        key={match.index}
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-500 underline hover:text-blue-700"
      >
        {url}
      </a>,
    );
    lastIndex = match.index + url.length;
  }
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }
  return parts;
};
