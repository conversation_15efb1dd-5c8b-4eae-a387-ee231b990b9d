-- CreateEnum
CREATE TYPE "medicalNecessitySetBy" AS ENUM ('doctor', 'patient', 'system');

-- CreateTable
CREATE TABLE "MedicalNecessity"
(
    "id"        TEXT                    NOT NULL,
    "patientId" TEXT                    NOT NULL,
    "setBy"     "medicalNecessitySetBy" NOT NULL DEFAULT 'patient',
    "necessity" TEXT                    NOT NULL,
    "createdAt" TIMESTAMP(3)            NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3)            NOT NULL,

    CONSTRAINT "MedicalNecessity_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MedicalNecessity"
    ADD CONSTRAINT "MedicalNecessity_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
