-- AlterTable
ALTER TABLE "Patient"
    ADD COLUMN "onboardingVersion" TEXT DEFAULT 'legacy-v1';

-- Update patients with questionnaire version 2 to legacy-v2
UPDATE "Patient" p
SET "onboardingVersion" = 'legacy-v2'
FROM "Questionnaire" q
WHERE p."questionnaireId" = q."id"
  AND q."version" = 2
  AND q."type" = 'onboarding';

-- Remove default value so new records must explicitly set the version
ALTER TABLE "Patient"
    ALTER COLUMN "onboardingVersion" DROP DEFAULT;

-- DropForeignKey
ALTER TABLE "Patient"
    DROP CONSTRAINT "Patient_questionnaireId_fkey";

-- AlterTable
ALTER TABLE "Patient"
    DROP COLUMN "questionnaireId";


-- DropIndex
DROP INDEX "PatientWaitingList_email_key";

-- CreateIndex
CREATE UNIQUE INDEX "PatientWaitingList_email_stateId_key" ON "PatientWaitingList" ("email", "stateId");