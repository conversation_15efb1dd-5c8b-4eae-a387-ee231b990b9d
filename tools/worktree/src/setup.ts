import { exec } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { promisify } from 'util';
import { input, search } from '@inquirer/prompts';
import chalk from 'chalk';
import { Command } from 'commander';
import ora from 'ora';

import { cleanNameForFilesystem } from './utils';

const execAsync = promisify(exec);

interface CommandOptions {
  name?: string;
  branch?: string;
  noOpen?: boolean;
}

export const styles = {
  header: chalk.bold.cyan,
  success: chalk.green,
  warning: chalk.yellow,
  error: chalk.red,
  info: chalk.cyan,
  muted: chalk.gray,
  command: chalk.magenta,
};

// Configuration
const MAIN_REPO = '/Users/<USER>/Willow';
const WORKTREE_BASE_DIR = '/Users/<USER>/Willow-worktrees';
const TMUXINATOR_CONFIG_DIR = '/Users/<USER>/.config/tmuxinator';

// Base ports
const BASE_PORT_API = 8080; // API base port
const BASE_PORT_PATIENTS = 3000; // Next.js default port
const BASE_PORT_DOCTORS = 3001; // Next.js default port + 1
const BASE_PORT_ADMIN = 3002; // Next.js default port + 2
const PORT_INCREMENT = 10;

// Default system service ports
const PORT_POSTGRES = 5432; // Default PostgreSQL port
const PORT_REDIS = 6379; // Default Redis port
const PORT_AWS = 4566; // Default AWS localstack port

process.on('SIGINT', () => {
  process.exit(0);
});

/**
 * Check if an application is available
 */
async function isAppAvailable(appName: string): Promise<boolean> {
  try {
    await execAsync(`osascript -e 'tell application "${appName}" to get name'`);
    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Get the best available terminal application
 */
async function getBestTerminalApp(): Promise<string> {
  // Check in order of preference: Ghostty -> iTerm2 -> Terminal
  if (await isAppAvailable('Ghostty')) {
    return 'Ghostty';
  }
  if (await isAppAvailable('iTerm')) {
    return 'iTerm';
  }
  return 'Terminal';
}

/**
 * Open terminal in new tab and start tmux session
 */
async function openTerminalInNewTab(worktreeNameClean: string): Promise<void> {
  try {
    const sessionName = `willow-${worktreeNameClean}`;

    // Get the best available terminal app
    const terminalApp = await getBestTerminalApp();

    // Use tmuxinator to start the session
    const tmuxCommand = `tmuxinator start ${sessionName}`;

    if (terminalApp === 'iTerm') {
      // iTerm2 specific script - create new tab
      await execAsync(
        `osascript -e 'tell application "iTerm" to activate' -e 'tell application "iTerm" to create tab with default profile' -e 'tell current session of current tab of current window of application "iTerm" to write text "${tmuxCommand}"'`,
      );
    } else {
      // Ghostty and Terminal use the same approach with System Events - open new tab
      await execAsync(
        `osascript -e 'tell application "${terminalApp}" to activate' -e 'tell application "System Events" to keystroke "t" using command down' -e 'delay 0.5' -e 'tell application "System Events" to keystroke "${tmuxCommand}" & return'`,
      );
    }
  } catch (error) {
    console.error(styles.error(`Failed to open terminal in new tab:`, error));
  }
}

async function run(options: CommandOptions) {
  try {
    console.log(styles.header('Willow Worktree Setup'));

    // Check if Docker services are running first
    let spinner = ora('Checking if Docker services are running...').start();
    const servicesRunning = await areDockerServicesRunning();

    if (!servicesRunning) {
      spinner.fail('Docker services are not running');
      console.error(
        styles.error(
          'Docker services must be running before setting up a worktree.',
        ),
      );
      console.error(
        styles.error(
          'Please start Docker services from the main repository with: docker compose up -d',
        ),
      );
      throw new Error('Docker services are not running');
    }

    spinner.succeed('Docker services are running');

    // Validate if we have both required options for non-interactive mode
    if (options.branch && !options.name) {
      throw new Error('When using --branch, you must also provide --name');
    }

    // Get branch name first
    let branch = options.branch;
    let isRemoteBranch = false;

    if (branch) {
      // Non-interactive mode
      console.log(styles.info('Running in non-interactive mode...'));

      // Validate branch name format
      const branchValidation = validateBranchName(branch);
      if (branchValidation !== true) {
        throw new Error(`Invalid branch name: ${branchValidation}`);
      }

      // Check if branch is already in use by another worktree
      if (await isBranchInUse(branch)) {
        throw new Error(
          `Branch '${branch}' is already in use by another worktree`,
        );
      }

      // Check if branch exists
      const branchInfo = await doesBranchExist(branch);

      if (!branchInfo.exists) {
        // Branch doesn't exist, create it from staging
        console.log(
          styles.info(
            `Branch '${branch}' does not exist. Creating from 'staging'...`,
          ),
        );

        try {
          // Fetch latest staging first
          await execAsync(`git -C "${MAIN_REPO}" fetch origin staging`);
          // Create new branch from origin/staging
          await execAsync(
            `git -C "${MAIN_REPO}" branch "${branch}" origin/staging`,
          );
          console.log(
            styles.success(`✅ Created new branch '${branch}' from staging`),
          );
        } catch (error) {
          throw new Error(
            `Failed to create branch '${branch}' from staging: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
      } else if (branchInfo.isRemote) {
        // Remote branch exists, create local tracking branch
        isRemoteBranch = true;
        const localBranchName = branch.replace(/^[^/]+\//, '');
        console.log(
          styles.info(
            `Creating local tracking branch '${localBranchName}' for remote branch '${branch}'...`,
          ),
        );

        try {
          await execAsync(
            `git -C "${MAIN_REPO}" branch "${localBranchName}" "${branch}"`,
          );
          branch = localBranchName;
          console.log(
            styles.success(
              `✅ Local tracking branch '${localBranchName}' created successfully`,
            ),
          );
        } catch (error) {
          throw new Error(
            `Failed to create local tracking branch: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
      } else {
        // Branch exists locally, just use it
        console.log(styles.info(`Using existing local branch '${branch}'`));
      }
    } else {
      // Interactive mode - existing logic
      // Get all local and remote branches
      const allBranches = await getAllBranches();

      // Create options array with create new branch option first
      const branchOptions = [
        '🌱 Create new branch',
        ...allBranches.map((b) => b.display),
      ];

      // Use search to select a branch
      const selectedOption = await search({
        message: 'Select a branch to checkout:',
        source: (term: string | undefined) => {
          if (!term) return branchOptions;
          return branchOptions.filter((option) =>
            option.toLowerCase().includes(term.toLowerCase()),
          );
        },
      });

      if (!selectedOption) throw new Error('No branch selected');

      // Check if user selected "Create new branch"
      if (selectedOption === '🌱 Create new branch') {
        // Prompt for new branch name
        const newBranchName = await input({
          message: 'Enter name for the new branch:',
          validate: (value) => {
            if (!value.trim()) {
              return 'Branch name is required';
            }
            if (!/^[a-zA-Z0-9/_-]+$/.test(value.trim())) {
              return 'Branch name can only contain letters, numbers, hyphens, underscores, and forward slashes';
            }
            return true;
          },
        });

        // Create the new branch from current branch
        const currentBranch = await getCurrentBranch();
        console.log(
          styles.info(
            `Creating new branch '${newBranchName}' from '${currentBranch}'...`,
          ),
        );

        try {
          await execAsync(`git -C "${MAIN_REPO}" branch "${newBranchName}"`);
          branch = newBranchName;
          console.log(
            styles.success(
              `✅ New branch '${newBranchName}' created successfully`,
            ),
          );
        } catch (error) {
          throw new Error(
            `Failed to create branch '${newBranchName}': ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
      } else {
        // Find the selected branch from our branches array
        const selectedBranch = allBranches.find(
          (b) => b.display === selectedOption,
        );
        if (!selectedBranch) {
          throw new Error('Selected branch not found');
        }

        branch = selectedBranch.value;
        isRemoteBranch = selectedBranch.type === 'remote';

        // If it's a remote branch, we need to create a local tracking branch
        if (isRemoteBranch) {
          const localBranchName = branch.replace(/^[^/]+\//, ''); // Remove remote name prefix
          console.log(
            styles.info(
              `Creating local tracking branch '${localBranchName}' for remote branch '${branch}'...`,
            ),
          );

          try {
            await execAsync(
              `git -C "${MAIN_REPO}" branch "${localBranchName}" "${branch}"`,
            );
            branch = localBranchName;
            console.log(
              styles.success(
                `✅ Local tracking branch '${localBranchName}' created successfully`,
              ),
            );
          } catch (error) {
            throw new Error(
              `Failed to create local tracking branch '${localBranchName}': ${error instanceof Error ? error.message : 'Unknown error'}`,
            );
          }
        }
      }

      if (!branch.trim()) {
        // This shouldn't happen since we now filter out the current branch
        branch = await getCurrentBranch();
        console.log(`Using current branch: ${styles.info(branch)}`);
      }
    }

    // Ensure branch is defined at this point
    if (!branch) {
      throw new Error('No branch selected or created');
    }

    // Handle name validation
    let name = options.name;
    if (name) {
      // Validate name if provided via CLI
      const nameValidation = validateWorktreeName(name);
      if (nameValidation !== true) {
        throw new Error(`Invalid worktree name: ${nameValidation}`);
      }
    } else {
      // Interactive mode for name
      const sanitizedDefault = cleanNameForFilesystem(branch).substring(0, 16);
      name = await input({
        message: 'Enter a name for your worktree (max 16 characters):',
        default: sanitizedDefault,
        validate: (value) => {
          if (!value.trim()) {
            return 'Name is required';
          }
          if (value.trim().length > 16) {
            return 'Name must be 16 characters or less';
          }
          return true;
        },
      });
    }

    // Ensure name is defined at this point
    if (!name) {
      throw new Error('No worktree name provided');
    }

    // Clean worktree name for filesystem use
    const worktreeNameClean = cleanNameForFilesystem(name);
    const worktreeDir = path.join(WORKTREE_BASE_DIR, worktreeNameClean);

    // Find existing worktrees to determine index using hole-filling strategy
    spinner = ora(
      'Checking existing worktrees and finding available port set...',
    ).start();
    const existingWorktrees = await getExistingWorktrees();
    const worktreeIndex = await findAvailableWorktreeIndex();
    spinner.succeed(
      `Found available port set at index ${worktreeIndex} (${existingWorktrees.length} existing worktrees)`,
    );

    // Calculate ports
    const ports = calculatePorts(worktreeIndex);

    // Display setup information
    console.log(styles.info('\nSetting up environment for worktree:'), name);
    console.log(styles.info('Working directory:'), worktreeDir);
    console.log(styles.info('Branch:'), branch);
    console.log(styles.info('Application Ports:'));
    console.log(`  API:       ${ports.portApi}`);
    console.log(`  Patients:  ${ports.portPatients}`);
    console.log(`  Doctors:   ${ports.portDoctors}`);
    console.log(`  Admin:     ${ports.portAdmin}`);
    console.log(styles.info('\nShared Docker Services:'));
    console.log(`  Postgres:  ${PORT_POSTGRES}`);
    console.log(`  Redis:     ${PORT_REDIS}`);
    console.log(`  AWS:       ${PORT_AWS}`);

    // Create worktree directory if it doesn't exist
    await fs.mkdir(WORKTREE_BASE_DIR, { recursive: true });

    // Check if worktree already exists and clean it up
    try {
      await fs.access(worktreeDir);

      console.log(
        styles.warning(`\nWorktree directory ${worktreeDir} already exists.`),
      );
      console.log(styles.info('Cleaning up existing worktree...'));

      // First, try to remove the git worktree properly
      try {
        await execAsync(
          `git -C "${MAIN_REPO}" worktree remove "${worktreeDir}" --force`,
        );
        console.log(styles.success('Git worktree removed successfully'));
      } catch (_gitError) {
        // If git worktree remove fails, it might not be a valid worktree
        console.log(
          styles.info(
            'Git worktree removal failed, removing directory manually...',
          ),
        );
      }

      // Remove the directory if it still exists
      try {
        await fs.rm(worktreeDir, { recursive: true, force: true });
        console.log(styles.success('Directory removed successfully'));
      } catch (rmError) {
        throw new Error(
          `Failed to remove existing worktree directory: ${rmError instanceof Error ? rmError.message : String(rmError)}`,
        );
      }
    } catch (error) {
      // If the error is because the directory doesn't exist, that's what we want
      if (error instanceof Error && error.message.includes('ENOENT')) {
        // Directory doesn't exist, continue with setup
      } else if (
        error instanceof Error &&
        error.message.includes('Failed to remove')
      ) {
        // Re-throw removal errors
        throw error;
      }
      // For other errors, continue
    }

    // Try to rebase against staging in the main repo before creating worktree
    spinner = ora('Attempting to rebase against staging...').start();
    try {
      const rebaseResult = await tryRebaseAgainstStaging(branch);
      if (rebaseResult.success) {
        spinner.succeed('Successfully rebased against staging');
      } else {
        spinner.warn(
          `Skipped rebase: ${rebaseResult.reason}. Continuing with original branch state.`,
        );
      }
    } catch (error) {
      spinner.warn(
        `Failed to rebase: ${error instanceof Error ? error.message : 'Unknown error'}. Continuing with original branch state.`,
      );
    }

    // Create git worktree
    spinner = ora('Creating git worktree...').start();
    try {
      await createGitWorktree(worktreeDir, branch);
      spinner.succeed('Git worktree created successfully');
    } catch (error) {
      spinner.fail('Failed to create git worktree');
      throw error;
    }

    // Copy and modify .env file
    spinner = ora('Copying and modifying .env file...').start();
    try {
      await setupEnvironmentFile(worktreeDir, worktreeNameClean, ports);
      spinner.succeed('Environment file created successfully');
    } catch (error) {
      spinner.fail('Failed to set up environment file');
      throw error;
    }

    // Create tmuxinator config if available
    spinner = ora('Checking for tmuxinator...').start();
    const tmuxinatorConfigured = await setupTmuxinatorConfig(
      worktreeDir,
      worktreeNameClean,
      ports,
    );
    if (tmuxinatorConfigured) {
      spinner.succeed('Tmuxinator config created successfully');
    } else {
      spinner.info(
        'Tmuxinator not found or not configured, skipping tmuxinator setup',
      );
    }

    // Install dependencies
    spinner = ora('Installing dependencies (this may take a while)...').start();
    try {
      await installDependencies(worktreeDir);
      spinner.succeed('Dependencies installed successfully');
    } catch (error) {
      spinner.fail('Failed to install dependencies');
      throw error;
    }

    // Create the database (services are already running from the check at the beginning)
    spinner = ora(
      `Creating database willow_${worktreeNameClean} on existing Postgres server...`,
    ).start();
    const dbResult = await setupDatabase(worktreeNameClean);
    if (dbResult.success) {
      spinner.succeed('Database created successfully');
    } else {
      spinner.warn(
        `Database setup: ${dbResult.reason}. Continuing with restore and migrations...`,
      );
    }

    // Restore database from clean slate
    spinner = ora('Restoring database from clean slate...').start();
    try {
      await restoreDatabaseAndMigrate(worktreeDir, worktreeNameClean);
      spinner.succeed('Database restored successfully');
    } catch (error) {
      spinner.fail('Failed to restore database');
      throw error;
    }

    // Start tmuxinator session if it was configured
    if (tmuxinatorConfigured) {
      spinner = ora('Starting tmuxinator session...').start();
      try {
        await execAsync(
          `tmuxinator start willow-${worktreeNameClean} --no-attach`,
        );
        spinner.succeed('Tmuxinator session started successfully');
      } catch (error) {
        spinner.warn(
          'Could not start tmuxinator session, but continuing with setup',
        );
      }
    }

    // Copy IDE project files if they exist
    spinner = ora('Checking for WebStorm/IntelliJ project files...').start();
    const ideaFilesCopied = await copyIdeaFiles(
      worktreeDir,
      worktreeNameClean,
      ports,
    );
    if (ideaFilesCopied) {
      spinner.succeed('WebStorm/IntelliJ project files copied successfully');
    } else {
      spinner.info(
        'No WebStorm/IntelliJ project files found or copying failed, skipping',
      );
    }

    // Copy VS Code project files if they exist
    spinner = ora('Checking for VS Code project files...').start();
    const vscodeFilesCopied = await copyVscodeFiles(
      worktreeDir,
      worktreeNameClean,
    );
    if (vscodeFilesCopied) {
      spinner.succeed('VS Code project files copied successfully');
    } else {
      spinner.info(
        'No VS Code project files found or copying failed, skipping',
      );
    }

    // Copy non-version controlled files
    spinner = ora('Copying non-version controlled files...').start();
    const nonVersionFilesCopied =
      await copyNonVersionControlledFiles(worktreeDir);
    if (nonVersionFilesCopied) {
      spinner.succeed('Non-version controlled files copied successfully');
    } else {
      spinner.warn(
        'Some non-version controlled files could not be copied, but continuing',
      );
    }

    // Final success message
    console.log(styles.success('\n✅ Worktree setup complete for:'), name);

    // Open terminal automatically unless --no-open flag is provided
    if (!options.noOpen && tmuxinatorConfigured) {
      console.log(styles.info('\nOpening terminal with tmuxinator session...'));
      try {
        await openTerminalInNewTab(worktreeNameClean);
        console.log(styles.success('✅ Terminal opened successfully'));
      } catch (error) {
        console.log(
          styles.warning(
            'Could not automatically open terminal, but setup completed successfully',
          ),
        );
      }
    }

    // Show different startup instructions based on tmuxinator availability
    if (tmuxinatorConfigured) {
      console.log('\nTo start your environment with tmuxinator, run:');
      console.log(
        styles.command(`  tmuxinator start willow-${worktreeNameClean}`),
      );
    } else {
      console.log('\nTo start your environment manually:');
      console.log(styles.command(`  # In one terminal`));
      console.log(
        styles.command(
          `  cd ${worktreeDir} && API_PORT=${ports.portApi} pp -F api dev`,
        ),
      );
      console.log(styles.command(`  # In another terminal`));
      console.log(
        styles.command(
          `  cd ${worktreeDir} && PATIENTS_PORT=${ports.portPatients} pp -F patients dev`,
        ),
      );
      console.log(styles.command(`  # In another terminal`));
      console.log(
        styles.command(
          `  cd ${worktreeDir} && DOCTORS_PORT=${ports.portDoctors} pp -F doctors dev`,
        ),
      );
      console.log(styles.command(`  # And so on for other services`));
    }
    console.log('\nYour applications will be available at:');
    console.log(`  API:       http://localhost:${ports.portApi}`);
    console.log(`  Patients:  http://localhost:${ports.portPatients}`);
    console.log(`  Doctors:   http://localhost:${ports.portDoctors}`);
    console.log(`  Admin:     http://localhost:${ports.portAdmin}`);
    console.log('\nDatabase connection:');
    console.log(`  Host: localhost`);
    console.log(`  Port: ${PORT_POSTGRES}`);
    console.log(`  Database: willow_${worktreeNameClean}`);
    console.log(
      `  Status: Clean slate database restored and migrations applied`,
    );
    console.log('\nRedis cache:');
    console.log(`  Prefix: ${worktreeNameClean}:local:`);
    console.log(
      `  (All Redis keys will be automatically prefixed to isolate cache data)`,
    );
    console.log("\nRemember to run the teardown script when you're done:");
    console.log(styles.command(`  ./teardown-worktree.sh ${name}`));
  } catch (error) {
    // Only show the error message, not the full stack trace
    if (error instanceof Error) {
      console.error(styles.error(error.message));
    } else {
      console.error(styles.error(String(error)));
    }
    process.exit(1);
  }
}

/**
 * Get the current git branch
 */
async function getCurrentBranch(): Promise<string> {
  const { stdout } = await execAsync(
    `git -C "${MAIN_REPO}" branch --show-current`,
  );
  return stdout.trim();
}

/**
 * Validate worktree name
 */
function validateWorktreeName(name: string): string | true {
  if (!name.trim()) {
    return 'Name is required';
  }
  if (name.trim().length > 16) {
    return 'Name must be 16 characters or less';
  }
  return true;
}

/**
 * Validate branch name format
 */
function validateBranchName(branch: string): string | true {
  if (!branch.trim()) {
    return 'Branch name is required';
  }
  if (!/^[a-zA-Z0-9/_-]+$/.test(branch.trim())) {
    return 'Branch name can only contain letters, numbers, hyphens, underscores, and forward slashes';
  }
  return true;
}

/**
 * Check if a branch is already in use by another worktree
 */
async function isBranchInUse(branch: string): Promise<boolean> {
  try {
    const { stdout } = await execAsync('git worktree list --porcelain');
    const lines = stdout.split('\n');

    for (const line of lines) {
      if (line.startsWith('branch ')) {
        const worktreeBranch = line.substring(7).trim(); // Remove 'branch ' prefix
        if (
          worktreeBranch === `refs/heads/${branch}` ||
          worktreeBranch === branch
        ) {
          return true;
        }
      }
    }
    return false;
  } catch (error) {
    // If git worktree list fails, assume branch is not in use
    return false;
  }
}

/**
 * Check if a branch exists locally or remotely
 */
async function doesBranchExist(
  branch: string,
): Promise<{ exists: boolean; isRemote: boolean }> {
  try {
    // First check if it exists locally
    try {
      await execAsync(`git -C "${MAIN_REPO}" rev-parse --verify "${branch}"`);
      return { exists: true, isRemote: false };
    } catch {
      // Not a local branch, check remotes
    }

    // Check if it exists as a remote branch
    const { stdout } = await execAsync(
      `git -C "${MAIN_REPO}" branch -r --format='%(refname:short)'`,
    );
    const remoteBranches = stdout
      .split('\n')
      .map((b) => b.trim())
      .filter((b) => b.length > 0);

    if (remoteBranches.includes(branch)) {
      return { exists: true, isRemote: true };
    }

    // Also check without origin/ prefix
    const branchWithoutOrigin = branch.replace(/^origin\//, '');
    if (remoteBranches.some((rb) => rb === `origin/${branchWithoutOrigin}`)) {
      return { exists: true, isRemote: true };
    }

    return { exists: false, isRemote: false };
  } catch (error) {
    // If any git command fails, assume branch doesn't exist
    return { exists: false, isRemote: false };
  }
}

/**
 * Get all local branches sorted by most recently checked out, excluding the current branch
 */
async function getLocalBranches(): Promise<string[]> {
  try {
    // Get the current branch first
    const currentBranch = await getCurrentBranch();

    // Use git for-each-ref to get branches sorted by committerdate (most recent first)
    const { stdout } = await execAsync(
      `git -C "${MAIN_REPO}" for-each-ref --sort=-committerdate refs/heads/ --format='%(refname:short)'`,
    );

    return stdout
      .split('\n')
      .map((branch) => branch.trim())
      .filter((branch) => branch.length > 0 && branch !== currentBranch);
  } catch (error) {
    // Fallback to unsorted branches if the above command fails
    console.warn(
      styles.warning(
        'Failed to sort branches by recency, using default ordering.',
      ),
    );

    // Get the current branch
    const currentBranch = await getCurrentBranch();

    const { stdout } = await execAsync(
      `git -C "${MAIN_REPO}" branch --format='%(refname:short)'`,
    );
    return stdout
      .split('\n')
      .map((branch) => branch.trim())
      .filter((branch) => branch.length > 0 && branch !== currentBranch);
  }
}

/**
 * Get all remote branches sorted by most recently checked out
 */
async function getRemoteBranches(): Promise<string[]> {
  try {
    // Use git for-each-ref to get remote branches sorted by committerdate (most recent first)
    const { stdout } = await execAsync(
      `git -C "${MAIN_REPO}" for-each-ref --sort=-committerdate refs/remotes/ --format='%(refname:short)'`,
    );

    return stdout
      .split('\n')
      .map((branch) => branch.trim())
      .filter((branch) => branch.length > 0 && !branch.endsWith('/HEAD'));
  } catch (error) {
    // Fallback to unsorted remote branches if the above command fails
    console.warn(
      styles.warning(
        'Failed to sort remote branches by recency, using default ordering.',
      ),
    );

    const { stdout } = await execAsync(
      `git -C "${MAIN_REPO}" branch -r --format='%(refname:short)'`,
    );
    return stdout
      .split('\n')
      .map((branch) => branch.trim())
      .filter((branch) => branch.length > 0 && !branch.endsWith('/HEAD'));
  }
}

/**
 * Check if GitHub CLI is available
 */
async function isGitHubCliAvailable(): Promise<boolean> {
  try {
    await execAsync('which gh');
    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Get PR information for branches using GitHub CLI
 */
async function getPrInformation(): Promise<
  Map<string, { number: number; title: string }>
> {
  const prMap = new Map<string, { number: number; title: string }>();

  try {
    const { stdout } = await execAsync(
      `cd "${MAIN_REPO}" && gh pr list --json headRefName,title,number --limit 100`,
      { env: { ...process.env, NO_COLOR: '1' } },
    );

    // Clean the output by removing ANSI escape sequences and other terminal artifacts
    const cleanOutput = stdout
      // eslint-disable-next-line no-control-regex
      .replace(/\x1b\[[0-9;]*[mGKH]/g, '') // Remove ANSI escape sequences
      // eslint-disable-next-line no-control-regex
      .replace(/\]11;[^\x07]*\x07/g, '') // Remove OSC sequences
      .replace(/\[6n/g, '') // Remove cursor position queries
      .replace(/\[\?1[hl]=/g, '') // Remove cursor mode changes
      .replace(/\[K/g, '') // Remove clear line sequences
      .trim();

    interface PullRequest {
      headRefName: string;
      number: number;
      title: string;
    }

    const prs = JSON.parse(cleanOutput) as PullRequest[];

    prs.forEach((pr) => {
      prMap.set(pr.headRefName, {
        number: pr.number,
        title: pr.title,
      });
    });
  } catch (error) {
    console.warn(
      styles.warning(
        'Failed to fetch PR information from GitHub CLI, continuing without PR data.',
      ),
    );
  }

  return prMap;
}

/**
 * Get both local and remote branches, formatted for display
 */
async function getAllBranches(): Promise<
  { value: string; display: string; type: 'local' | 'remote' }[]
> {
  const [localBranches, remoteBranches, githubCliAvailable] = await Promise.all(
    [getLocalBranches(), getRemoteBranches(), isGitHubCliAvailable()],
  );

  // Get PR information if GitHub CLI is available
  const prInfo: Map<string, { number: number; title: string }> =
    githubCliAvailable
      ? await getPrInformation()
      : new Map<string, { number: number; title: string }>();

  const branches: {
    value: string;
    display: string;
    type: 'local' | 'remote';
  }[] = [];

  // Add local branches
  localBranches.forEach((branch) => {
    const pr = prInfo.get(branch);
    const prDisplay = pr
      ? ` [PR #${pr.number}: ${pr.title.substring(0, 50)}${pr.title.length > 50 ? '...' : ''}]`
      : '';

    branches.push({
      value: branch,
      display: `📍 ${branch} (local)${prDisplay}`,
      type: 'local',
    });
  });

  // Add remote branches, but exclude ones that have local counterparts
  remoteBranches.forEach((remoteBranch) => {
    const branchName = remoteBranch.replace(/^[^/]+\//, ''); // Remove remote name prefix
    const hasLocalCounterpart = localBranches.includes(branchName);

    if (!hasLocalCounterpart) {
      const pr = prInfo.get(branchName);
      const prDisplay = pr
        ? ` [PR #${pr.number}: ${pr.title.substring(0, 50)}${pr.title.length > 50 ? '...' : ''}]`
        : '';

      branches.push({
        value: remoteBranch,
        display: `🌐 ${remoteBranch} (remote)${prDisplay}`,
        type: 'remote',
      });
    }
  });

  return branches;
}

/**
 * Get existing worktrees
 */
async function getExistingWorktrees(): Promise<string[]> {
  try {
    const entries = await fs.readdir(WORKTREE_BASE_DIR);
    const directories: string[] = [];
    for (const entry of entries) {
      const stat = await fs.stat(path.join(WORKTREE_BASE_DIR, entry));
      if (stat.isDirectory()) {
        directories.push(entry);
      }
    }
    return directories;
  } catch (_error) {
    // Directory might not exist yet
    return [];
  }
}

/**
 * Get environment information for a worktree from its .env file
 */
async function getWorktreeEnvironmentInfo(worktreeDir: string): Promise<{
  apiPort: number | null;
  postgresPort: number | null;
  patientsPort: number | null;
  doctorsPort: number | null;
  adminPort: number | null;
}> {
  const envFilePath = path.join(worktreeDir, '.env');

  try {
    const envContent = await fs.readFile(envFilePath, 'utf-8');

    // Extract API port
    const apiPortMatch = /^API_PORT=(.*)$/m.exec(envContent);
    const apiPort = apiPortMatch?.[1] ? parseInt(apiPortMatch[1], 10) : null;

    // Extract Postgres port
    const postgresPortMatch = /^POSTGRES_PORT=(.*)$/m.exec(envContent);
    const postgresPort = postgresPortMatch?.[1]
      ? parseInt(postgresPortMatch[1], 10)
      : null;

    // Extract frontend ports - these might not be explicitly set in .env
    // so we need to calculate them based on the worktree index
    const patientsPortMatch = /^PATIENTS_PORT=(.*)$/m.exec(envContent);
    const doctorsPortMatch = /^DOCTORS_PORT=(.*)$/m.exec(envContent);
    const adminPortMatch = /^ADMIN_PORT=(.*)$/m.exec(envContent);

    // If ports aren't found in .env, calculate them based on API port offset
    let patientsPort: number | null = null;
    let doctorsPort: number | null = null;
    let adminPort: number | null = null;

    if (patientsPortMatch?.[1]) {
      patientsPort = parseInt(patientsPortMatch[1], 10);
    } else if (apiPort !== null) {
      // Calculate based on API port offset (API base is 8080, patients base is 3000)
      const portOffset = apiPort - BASE_PORT_API;
      patientsPort = BASE_PORT_PATIENTS + portOffset;
    }

    if (doctorsPortMatch?.[1]) {
      doctorsPort = parseInt(doctorsPortMatch[1], 10);
    } else if (apiPort !== null) {
      const portOffset = apiPort - BASE_PORT_API;
      doctorsPort = BASE_PORT_DOCTORS + portOffset;
    }

    if (adminPortMatch?.[1]) {
      adminPort = parseInt(adminPortMatch[1], 10);
    } else if (apiPort !== null) {
      const portOffset = apiPort - BASE_PORT_API;
      adminPort = BASE_PORT_ADMIN + portOffset;
    }

    return {
      apiPort,
      postgresPort,
      patientsPort,
      doctorsPort,
      adminPort,
    };
  } catch (_error) {
    return {
      apiPort: null,
      postgresPort: null,
      patientsPort: null,
      doctorsPort: null,
      adminPort: null,
    };
  }
}

/**
 * Find all used port sets from existing worktrees
 */
async function getUsedPortSets(): Promise<Set<number>> {
  const usedIndexes = new Set<number>();

  try {
    const worktreeNames = await getExistingWorktrees();

    for (const worktreeName of worktreeNames) {
      const worktreeDir = path.join(WORKTREE_BASE_DIR, worktreeName);
      const envInfo = await getWorktreeEnvironmentInfo(worktreeDir);

      if (envInfo.apiPort !== null) {
        // Calculate the index from the API port
        const portOffset = envInfo.apiPort - BASE_PORT_API;
        const index = portOffset / PORT_INCREMENT - 1; // Reverse of calculatePorts logic

        if (Number.isInteger(index) && index >= 0) {
          usedIndexes.add(index);
        }
      }
    }
  } catch (_error) {
    // If we can't read existing worktrees, return empty set
  }

  return usedIndexes;
}

/**
 * Find the first available worktree index (hole-filling strategy)
 */
async function findAvailableWorktreeIndex(): Promise<number> {
  const usedIndexes = await getUsedPortSets();

  // Find the first gap (hole) in the sequence
  let index = 0;
  while (usedIndexes.has(index)) {
    index++;
  }

  return index;
}

/**
 * Calculate ports for the worktree
 */
function calculatePorts(worktreeIndex: number): {
  portApi: number;
  portWebDebug: number;
  portCliDebug: number;
  portToolsDebug: number;
  portPatients: number;
  portDoctors: number;
  portAdmin: number;
} {
  // Calculate ports using consecutive strategy: default + ((index+1) * increment)
  // even the first worktree gets a unique port set
  const adjustedIndex = worktreeIndex + 1;
  const portOffset = adjustedIndex * PORT_INCREMENT;

  return {
    portApi: BASE_PORT_API + portOffset,
    portWebDebug: BASE_PORT_API + portOffset + 1,
    portCliDebug: BASE_PORT_API + portOffset + 2,
    portToolsDebug: BASE_PORT_API + portOffset + 3,
    portPatients: BASE_PORT_PATIENTS + portOffset,
    portDoctors: BASE_PORT_DOCTORS + portOffset,
    portAdmin: BASE_PORT_ADMIN + portOffset,
  };
}

/**
 * Copy non-version controlled files to the worktree
 */
async function copyNonVersionControlledFiles(
  worktreeDir: string,
): Promise<boolean> {
  let allSuccessful = true;

  // Files and directories to copy
  const itemsToCopy = [
    { source: '.claude', target: '.claude', type: 'directory' },
    {
      source: 'resources/snapshots/local.sql',
      target: 'resources/snapshots/local.sql',
      type: 'file',
    },
    {
      source: 'resources/tickets',
      target: 'resources/tickets',
      type: 'directory',
    },
  ];

  // Files to symlink
  const itemsToSymlink = [
    {
      source: '.claude/settings.local.json',
      target: '.claude/settings.local.json',
    },
    {
      source: 'resources/snapshots/production.sql',
      target: 'resources/snapshots/production.sql',
    },
    {
      source: 'resources/snapshots/staging.sql',
      target: 'resources/snapshots/staging.sql',
    },
    {
      source: '.env.production',
      target: '.env.production',
    },
    {
      source: '.env.staging',
      target: '.env.staging',
    },
    {
      source: '.mcp.json',
      target: '.mcp.json',
    },
  ];

  for (const item of itemsToCopy) {
    try {
      const sourcePath = path.join(MAIN_REPO, item.source);
      const targetPath = path.join(worktreeDir, item.target);

      // Check if source exists
      try {
        await fs.access(sourcePath);
      } catch (_error) {
        // Source doesn't exist, skip this item
        console.warn(styles.warning(`  Skipping ${item.source} (not found)`));
        continue;
      }

      if (item.type === 'directory') {
        // Copy directory recursively
        await copyDirectoryRecursive(sourcePath, targetPath);
      } else {
        // Copy file
        // Ensure target directory exists
        const targetDir = path.dirname(targetPath);
        await fs.mkdir(targetDir, { recursive: true });
        await fs.copyFile(sourcePath, targetPath);
      }
    } catch (error) {
      console.warn(
        styles.warning(
          `  Failed to copy ${item.source}: ${error instanceof Error ? error.message : String(error)}`,
        ),
      );
      allSuccessful = false;
    }
  }

  // Create symlinks for specific files
  for (const item of itemsToSymlink) {
    try {
      const sourcePath = path.join(MAIN_REPO, item.source);
      const targetPath = path.join(worktreeDir, item.target);

      // Check if source exists
      try {
        await fs.access(sourcePath);
      } catch (_error) {
        // Source doesn't exist, skip this item
        console.warn(
          styles.warning(`  Skipping symlink for ${item.source} (not found)`),
        );
        continue;
      }

      // Remove the target file if it was already copied
      try {
        await fs.unlink(targetPath);
      } catch (_error) {
        // File doesn't exist, that's fine
      }

      // Ensure target directory exists
      const targetDir = path.dirname(targetPath);
      await fs.mkdir(targetDir, { recursive: true });

      // Create symlink
      await fs.symlink(sourcePath, targetPath);
    } catch (error) {
      console.warn(
        styles.warning(
          `  Failed to create symlink for ${item.source}: ${error instanceof Error ? error.message : String(error)}`,
        ),
      );
      allSuccessful = false;
    }
  }

  return allSuccessful;
}

/**
 * Copy directory recursively
 */
async function copyDirectoryRecursive(
  sourceDir: string,
  targetDir: string,
): Promise<void> {
  // Create target directory
  await fs.mkdir(targetDir, { recursive: true });

  // Read source directory
  const entries = await fs.readdir(sourceDir, { withFileTypes: true });

  for (const entry of entries) {
    const sourcePath = path.join(sourceDir, entry.name);
    const targetPath = path.join(targetDir, entry.name);

    // Skip settings.local.json as it will be symlinked
    if (sourcePath.endsWith('.claude/settings.local.json')) {
      continue;
    }

    if (entry.isDirectory()) {
      // Recursively copy subdirectory
      await copyDirectoryRecursive(sourcePath, targetPath);
    } else if (entry.isFile()) {
      // Copy file
      await fs.copyFile(sourcePath, targetPath);
    }
  }
}

/**
 * Copy VS Code project files if they exist
 */
async function copyVscodeFiles(
  worktreeDir: string,
  worktreeNameClean: string,
): Promise<boolean> {
  try {
    const vscodeDir = path.join(MAIN_REPO, '.vscode');
    const targetVscodeDir = path.join(worktreeDir, '.vscode');

    // Check if .vscode directory exists
    try {
      await fs.access(vscodeDir);
    } catch (_error) {
      // .vscode directory doesn't exist
      return false;
    }

    // Clean up any existing .vscode directory
    try {
      await fs.access(targetVscodeDir);
      await fs.rm(targetVscodeDir, { recursive: true, force: true });
      console.log(styles.info('  Removed existing .vscode directory'));
    } catch (_error) {
      // Directory doesn't exist, nothing to clean up
    }

    // Create target .vscode directory
    await fs.mkdir(targetVscodeDir, { recursive: true });

    // Copy all files in the .vscode directory
    const files = await fs.readdir(vscodeDir);
    for (const file of files) {
      const sourcePath = path.join(vscodeDir, file);
      const targetPath = path.join(targetVscodeDir, file);

      // Check if it's a file (not a directory)
      const stats = await fs.stat(sourcePath);
      if (stats.isFile()) {
        await fs.copyFile(sourcePath, targetPath);

        // Update project name in settings.json if it exists
        if (file === 'settings.json') {
          try {
            let settingsContent = await fs.readFile(targetPath, 'utf-8');
            const projectName = `Willow, Dash and ${worktreeNameClean}`;

            // Add or update project name setting
            if (settingsContent.includes('"window.title"')) {
              settingsContent = settingsContent.replace(
                /"window\.title"\s*:\s*"[^"]*"/,
                `"window.title": "${projectName}"`,
              );
            } else {
              // Add window.title setting before the last closing brace
              settingsContent = settingsContent.replace(
                /}$/,
                `,\n  "window.title": "${projectName}"\n}`,
              );
            }

            await fs.writeFile(targetPath, settingsContent);
          } catch (error) {
            // Continue if we can't update the settings
            console.warn(
              `Could not update VS Code project name in settings.json: ${error instanceof Error ? error.message : String(error)}`,
            );
          }
        }
      } else if (stats.isDirectory()) {
        // Recursively copy subdirectories if they exist
        await fs.mkdir(targetPath, { recursive: true });
        const subfiles = await fs.readdir(sourcePath);
        for (const subfile of subfiles) {
          const subSourcePath = path.join(sourcePath, subfile);
          const subTargetPath = path.join(targetPath, subfile);

          const subStats = await fs.stat(subSourcePath);
          if (subStats.isFile()) {
            await fs.copyFile(subSourcePath, subTargetPath);
          }
        }
      }
    }

    // Create or update VS Code workspace file
    try {
      const workspaceFilePath = path.join(
        worktreeDir,
        `${worktreeNameClean}.code-workspace`,
      );
      const workspaceContent = {
        folders: [{ path: '.' }],
        settings: { 'window.title': `Willow - ${worktreeNameClean}` },
      };

      await fs.writeFile(
        workspaceFilePath,
        JSON.stringify(workspaceContent, null, 2),
      );
    } catch (error) {
      // Continue if we can't create the workspace file
      console.warn(
        `Could not create VS Code workspace file: ${error instanceof Error ? error.message : String(error)}`,
      );
    }

    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Copy IDE project files if they exist
 */
async function copyIdeaFiles(
  worktreeDir: string,
  worktreeNameClean: string,
  ports?: {
    portApi: number;
    portWebDebug: number;
    portCliDebug: number;
    portToolsDebug: number;
    portPatients: number;
    portDoctors: number;
    portAdmin: number;
  },
): Promise<boolean> {
  try {
    const ideaDir = path.join(MAIN_REPO, '.idea');
    const targetIdeaDir = path.join(worktreeDir, '.idea');

    // Check if .idea directory exists
    try {
      await fs.access(ideaDir);
    } catch (_error) {
      // .idea directory doesn't exist
      return false;
    }

    // Clean up any existing .idea directory
    try {
      await fs.access(targetIdeaDir);
      await fs.rm(targetIdeaDir, { recursive: true, force: true });
      console.log(styles.info('  Removed existing .idea directory'));
    } catch (_error) {
      // Directory doesn't exist, nothing to clean up
    }

    // Create target .idea directory
    await fs.mkdir(targetIdeaDir, { recursive: true });

    // Files to copy
    const filesToCopy = [
      'workspace.xml',
      'dataSources.xml',
      'dataSources.local.xml',
      'prettier.xml',
      'jsLinters/eslint.xml',
    ];

    // Directories to copy recursively
    const dirsToCopy = [
      'codeStyles',
      'inspectionProfiles',
      'dataSources',
      'jsLinters',
    ];

    // Copy individual files
    for (const file of filesToCopy) {
      try {
        await fs.copyFile(
          path.join(ideaDir, file),
          path.join(targetIdeaDir, file),
        );
      } catch (_error) {
        // Skip if file doesn't exist
      }
    }

    // Update dataSources.xml to use the new database name
    try {
      const dataSourcesXmlPath = path.join(targetIdeaDir, 'dataSources.xml');
      let dataSourcesContent = await fs.readFile(dataSourcesXmlPath, 'utf-8');

      // Replace database name in the connection string
      dataSourcesContent = dataSourcesContent.replace(
        /********************************************,
        `***************************************_${worktreeNameClean}`,
      );

      // Replace database name in the data source name
      dataSourcesContent = dataSourcesContent.replace(
        /name="willow@localhost"/g,
        `name="willow_${worktreeNameClean}@localhost"`,
      );

      await fs.writeFile(dataSourcesXmlPath, dataSourcesContent);
    } catch (_error) {
      // Skip if file doesn't exist or can't be modified
    }

    // Update dataSources.local.xml to use the new database name
    try {
      const dataSourcesLocalXmlPath = path.join(
        targetIdeaDir,
        'dataSources.local.xml',
      );
      let dataSourcesLocalContent = await fs.readFile(
        dataSourcesLocalXmlPath,
        'utf-8',
      );

      // Replace database name in the data source name
      dataSourcesLocalContent = dataSourcesLocalContent.replace(
        /name="willow@localhost"/g,
        `name="willow_${worktreeNameClean}@localhost"`,
      );

      // Replace database name in the introspection scope
      dataSourcesLocalContent = dataSourcesLocalContent.replace(
        /<node kind="database" qname="willow" \/>/g,
        `<node kind="database" qname="willow_${worktreeNameClean}" />`,
      );

      await fs.writeFile(dataSourcesLocalXmlPath, dataSourcesLocalContent);
    } catch (_error) {
      // Skip if file doesn't exist or can't be modified
    }

    // Copy directories recursively
    for (const dir of dirsToCopy) {
      try {
        const sourceDir = path.join(ideaDir, dir);
        const targetDir = path.join(targetIdeaDir, dir);

        // Check if source directory exists
        await fs.access(sourceDir);

        // Create target directory
        await fs.mkdir(targetDir, { recursive: true });

        // Copy files in directory
        const files = await fs.readdir(sourceDir);
        for (const file of files) {
          const sourcePath = path.join(sourceDir, file);
          const targetPath = path.join(targetDir, file);

          // Check if it's a file (not a directory)
          const stats = await fs.stat(sourcePath);
          if (stats.isFile()) {
            await fs.copyFile(sourcePath, targetPath);
          }
        }
      } catch (_error) {
        // Skip if directory doesn't exist
      }
    }

    // Copy and rename Willow.iml
    try {
      await fs.copyFile(
        path.join(ideaDir, 'Willow.iml'),
        path.join(targetIdeaDir, `${worktreeNameClean}.iml`),
      );

      // Update modules.xml to reference the new .iml file
      let modulesContent = await fs.readFile(
        path.join(targetIdeaDir, 'modules.xml'),
        'utf-8',
      );

      // Update module name and file reference
      const projectName = `Willow - ${worktreeNameClean}`;
      modulesContent = modulesContent
        .replace(/Willow\.iml/g, `${worktreeNameClean}.iml`)
        // Update the module name in the modules.xml file
        .replace(
          /<module[^>]*name="Willow"[^>]*>/g,
          `<module name="${projectName}" />`,
        );

      await fs.writeFile(
        path.join(targetIdeaDir, 'modules.xml'),
        modulesContent,
      );

      // Update .name file if it exists
      try {
        const nameFilePath = path.join(targetIdeaDir, '.name');
        await fs.writeFile(nameFilePath, projectName);
      } catch (_nameError) {
        // Create .name file if it doesn't exist
        try {
          await fs.writeFile(path.join(targetIdeaDir, '.name'), projectName);
        } catch (_createError) {
          // Skip if we can't create the file
        }
      }
    } catch (_error) {
      // Skip if files don't exist
    }

    // Update workspace.xml with correct debug ports if ports are provided
    if (ports) {
      try {
        const workspaceXmlPath = path.join(targetIdeaDir, 'workspace.xml');
        let workspaceContent = await fs.readFile(workspaceXmlPath, 'utf-8');

        // Set project name in workspace.xml
        const projectName = `Willow - ${worktreeNameClean}`;

        // Update project name in workspace.xml if it contains the component tag
        if (workspaceContent.includes('<component name="ProjectName">')) {
          workspaceContent = workspaceContent.replace(
            /<component name="ProjectName">.*?<\/component>/s,
            `<component name="ProjectName">\n    <option name="name" value="${projectName}" />\n  </component>`,
          );
        } else {
          // Add ProjectName component if it doesn't exist
          workspaceContent = workspaceContent.replace(
            /<project /,
            `<project>\n  <component name="ProjectName">\n    <option name="name" value="${projectName}" />\n  </component>\n  `,
          );
        }

        workspaceContent = workspaceContent.replace(
          /<configuration name="api" type="ChromiumRemoteDebugType" factoryName="Chromium Remote" port="9229"/,
          `<configuration name="api" type="ChromiumRemoteDebugType" factoryName="Chromium Remote" port="${ports.portWebDebug}"`,
        );

        workspaceContent = workspaceContent.replace(
          /<configuration name="cli" type="ChromiumRemoteDebugType" factoryName="Chromium Remote" port="9230"/,
          `<configuration name="cli" type="ChromiumRemoteDebugType" factoryName="Chromium Remote" port="${ports.portCliDebug}"`,
        );

        workspaceContent = workspaceContent.replace(
          /<configuration name="tools" type="ChromiumRemoteDebugType" factoryName="Chromium Remote" port="9231"/,
          `<configuration name="tools" type="ChromiumRemoteDebugType" factoryName="Chromium Remote" port="${ports.portToolsDebug}"`,
        );

        workspaceContent = workspaceContent.replace(
          /<configuration name="admin" type="JavascriptDebugType" factoryName="Javascript Debugger" port="3002"/,
          `<configuration name="admin" type="JavascriptDebugType" factoryName="Javascript Debugger" port="${ports.portAdmin}"`,
        );

        workspaceContent = workspaceContent.replace(
          /<configuration name="doctor" type="JavascriptDebugType" factoryName="Javascript Debugger" port="3001"/,
          `<configuration name="doctor" type="JavascriptDebugType" factoryName="Javascript Debugger" port="${ports.portDoctors}"`,
        );

        workspaceContent = workspaceContent.replace(
          /<configuration name="patient" type="JavascriptDebugType" factoryName="Javascript Debugger" port="3000"/,
          `<configuration name="patient" type="JavascriptDebugType" factoryName="Javascript Debugger" port="${ports.portPatients}"`,
        );

        await fs.writeFile(workspaceXmlPath, workspaceContent);
      } catch (_error) {
        // Skip if workspace.xml doesn't exist or can't be modified
      }
    }

    // Copy dataSources directory (only .xml files)
    try {
      const dataSourcesDir = path.join(ideaDir, 'dataSources');
      const targetDataSourcesDir = path.join(targetIdeaDir, 'dataSources');

      // Check if dataSources directory exists
      await fs.access(dataSourcesDir);

      // Create target dataSources directory
      await fs.mkdir(targetDataSourcesDir, { recursive: true });

      // Copy all .xml files in the root of dataSources
      const files = await fs.readdir(dataSourcesDir);
      for (const file of files) {
        if (file.endsWith('.xml')) {
          await fs.copyFile(
            path.join(dataSourcesDir, file),
            path.join(targetDataSourcesDir, file),
          );
        }
      }
    } catch (_error) {
      // Skip if directory doesn't exist
    }

    return true;
  } catch (_error) {
    return false;
  }
}

async function createGitWorktree(
  worktreeDir: string,
  branch: string,
): Promise<void> {
  await execAsync(
    `git -C "${MAIN_REPO}" worktree add "${worktreeDir}" "${branch}"`,
  );
}

/**
 * Try to rebase the branch against staging in the main repository
 * Returns success status and reason if skipped/failed
 */
async function tryRebaseAgainstStaging(
  branch: string,
): Promise<{ success: boolean; reason?: string }> {
  try {
    // First, check if we're already on staging
    if (branch === 'staging') {
      return { success: false, reason: 'already on staging branch' };
    }

    // Store the current branch to return to it later
    const { stdout: originalBranch } = await execAsync(
      `git -C "${MAIN_REPO}" branch --show-current`,
    );

    // Check if there are any modified files in the main repo
    const { stdout: status } = await execAsync(
      `git -C "${MAIN_REPO}" status --porcelain`,
    );

    let hasStash = false;
    if (status.trim()) {
      // There are modified files, stash them
      await execAsync(
        `git -C "${MAIN_REPO}" stash push -m "worktree-rebase-stash"`,
      );
      hasStash = true;
    }

    try {
      // Checkout the branch we want to rebase
      await execAsync(`git -C "${MAIN_REPO}" checkout "${branch}"`);

      // Fetch latest staging
      try {
        await execAsync(`git -C "${MAIN_REPO}" fetch origin staging`);
      } catch (fetchError) {
        // If fetch fails, it's usually because staging has diverged
        // Return to original branch before returning
        await execAsync(
          `git -C "${MAIN_REPO}" checkout "${originalBranch.trim()}"`,
        );

        // Restore stash if we had one
        if (hasStash) {
          try {
            await execAsync(`git -C "${MAIN_REPO}" stash pop`);
          } catch {
            // Ignore stash pop errors
          }
        }

        return { success: false, reason: 'could not fetch latest staging' };
      }

      // Check if we're already up to date with staging
      const { stdout: mergeBase } = await execAsync(
        `git -C "${MAIN_REPO}" merge-base HEAD staging`,
      );
      const { stdout: stagingCommit } = await execAsync(
        `git -C "${MAIN_REPO}" rev-parse staging`,
      );

      if (mergeBase.trim() === stagingCommit.trim()) {
        // Return to original branch
        await execAsync(
          `git -C "${MAIN_REPO}" checkout "${originalBranch.trim()}"`,
        );

        // Restore stash if we had one
        if (hasStash) {
          try {
            await execAsync(`git -C "${MAIN_REPO}" stash pop`);
          } catch {
            // Ignore stash pop errors
          }
        }

        return { success: false, reason: 'already up to date' };
      }

      // Try to rebase
      // Use a non-throwing exec for the rebase
      const rebaseResult = await new Promise<{ code: number; output: string }>(
        (resolve) => {
          exec(
            `cd "${MAIN_REPO}" && git rebase staging`,
            { encoding: 'utf8' },
            (error, stdout, stderr) => {
              const output = stdout + stderr;
              resolve({
                code: error
                  ? typeof error === 'object' && 'code' in error
                    ? Number(error.code)
                    : 1
                  : 0,
                output,
              });
            },
          );
        },
      );

      if (rebaseResult.code === 0) {
        // Rebase succeeded
        // Return to original branch (now with rebased commits)
        await execAsync(
          `git -C "${MAIN_REPO}" checkout "${originalBranch.trim()}"`,
        );

        // Restore stash if we had one
        if (hasStash) {
          try {
            await execAsync(`git -C "${MAIN_REPO}" stash pop`);
          } catch {
            // Ignore stash pop errors - user can manually run "git stash pop" later
          }
        }

        return { success: true };
      } else if (
        rebaseResult.output.includes('CONFLICT') ||
        rebaseResult.output.includes('error: could not apply')
      ) {
        // Rebase had conflicts, abort it
        try {
          await execAsync(`git -C "${MAIN_REPO}" rebase --abort`);
        } catch {
          // Could not abort rebase cleanly
        }

        // Return to original branch
        await execAsync(
          `git -C "${MAIN_REPO}" checkout "${originalBranch.trim()}"`,
        );

        // Restore stash if we had one
        if (hasStash) {
          try {
            await execAsync(`git -C "${MAIN_REPO}" stash pop`);
          } catch {
            // Failed to restore stashed changes - user can manually run "git stash pop" later
          }
        }

        return { success: false, reason: 'conflicts detected' };
      } else {
        // Some other error
        // Try to abort rebase if it's in progress
        try {
          await execAsync(`git -C "${MAIN_REPO}" rebase --abort`);
        } catch {
          // Ignore abort errors
        }

        // Return to original branch
        await execAsync(
          `git -C "${MAIN_REPO}" checkout "${originalBranch.trim()}"`,
        );

        // Restore stash if we had one
        if (hasStash) {
          try {
            await execAsync(`git -C "${MAIN_REPO}" stash pop`);
          } catch {
            // Ignore stash pop errors
          }
        }

        return { success: false, reason: 'rebase failed' };
      }
    } catch (error) {
      // Something went wrong, try to clean up
      // Try to abort rebase if it's in progress
      try {
        await execAsync(`git -C "${MAIN_REPO}" rebase --abort`);
      } catch {
        // Ignore abort errors
      }

      // Try to return to original branch
      try {
        await execAsync(
          `git -C "${MAIN_REPO}" checkout "${originalBranch.trim()}"`,
        );
      } catch {
        // Could not return to original branch
      }

      // Try to restore stash if we had one
      if (hasStash) {
        try {
          await execAsync(`git -C "${MAIN_REPO}" stash pop`);
        } catch {
          // Failed to restore stashed changes - user can manually run "git stash pop" later
        }
      }

      // Return a clean error message instead of throwing
      return { success: false, reason: 'rebase failed' };
    }
  } catch (error) {
    // Outer catch for any other unexpected errors
    return { success: false, reason: 'could not complete rebase' };
  }
}

/**
 * Copy and modify .env file for the worktree
 */
async function setupEnvironmentFile(
  worktreeDir: string,
  worktreeNameClean: string,
  ports: {
    portApi: number;
    portWebDebug: number;
    portCliDebug: number;
    portToolsDebug: number;
    portPatients: number;
    portDoctors: number;
    portAdmin: number;
  },
): Promise<void> {
  // Copy .env file
  await fs.copyFile(
    path.join(MAIN_REPO, '.env'),
    path.join(worktreeDir, '.env'),
  );

  // Read the .env file
  let envContent = await fs.readFile(path.join(worktreeDir, '.env'), 'utf-8');

  // Update environment variables
  envContent = envContent
    .replace(/^API_PORT=.*/m, `API_PORT=${ports.portApi}`)
    .replace(/^POSTGRES_PORT=.*/m, `POSTGRES_PORT=${PORT_POSTGRES}`)
    .replace(/^POSTGRES_DB=.*/m, `POSTGRES_DB=willow_${worktreeNameClean}`)
    .replace(
      /^DATABASE_URL=.*/m,
      `DATABASE_URL="postgres://\${POSTGRES_USER}:\${POSTGRES_PASSWORD}@\${POSTGRES_HOST}:${PORT_POSTGRES}/willow_${worktreeNameClean}"`,
    )
    .replace(/^REDIS_PORT=.*/m, `REDIS_PORT=${PORT_REDIS}`)
    .replace(
      /^PATIENTS_URL=.*/m,
      `PATIENTS_URL=http://localhost:${ports.portPatients}`,
    )
    .replace(
      /^DOCTORS_URL=.*/m,
      `DOCTORS_URL=http://localhost:${ports.portDoctors}`,
    )
    .replace(/^ADMIN_URL=.*/m, `ADMIN_URL=http://localhost:${ports.portAdmin}`)
    .replace(
      /^NEXT_PUBLIC_API_URL=.*/m,
      `NEXT_PUBLIC_API_URL=http://localhost:${ports.portApi}`,
    )
    .replace(
      /^AWS_SQS_QUEUE_PREFIX=.*/m,
      `AWS_SQS_QUEUE_PREFIX="${worktreeNameClean}_\${ENVIRONMENT}"`,
    )
    .replace(
      /^AWS_SNS_TOPIC_PREFIX=.*/m,
      `AWS_SNS_TOPIC_PREFIX="${worktreeNameClean}_\${ENVIRONMENT}"`,
    )
    .replace(
      /^AWS_SQS_LOCALSTACK_ENDPOINT=.*/m,
      `AWS_SQS_LOCALSTACK_ENDPOINT=http://localhost:${PORT_AWS}`,
    )
    .replace(
      /^AWS_SNS_LOCALSTACK_ENDPOINT=.*/m,
      `AWS_SNS_LOCALSTACK_ENDPOINT=http://localhost:${PORT_AWS}`,
    )
    .replace(
      /^AWS_SNS_NOTIFICATION_API_URL=.*/m,
      `AWS_SNS_NOTIFICATION_API_URL="http://host.docker.internal:${ports.portApi}"`,
    )
    .replace(
      /^NEXT_PUBLIC_PATIENTS_URL=.*/m,
      `NEXT_PUBLIC_PATIENTS_URL=http://localhost:${ports.portPatients}`,
    );

  // Add AWS_ENDPOINT_OVERRIDE environment variable
  if (!envContent.includes('AWS_ENDPOINT_OVERRIDE=')) {
    envContent += `\nAWS_ENDPOINT_OVERRIDE=http://localhost:${PORT_AWS}`;
  }

  // Add WORKTREE_NAME for localtunnel subdomain and other uses
  if (!envContent.includes('WORKTREE_NAME=')) {
    envContent += `\nWORKTREE_NAME=${worktreeNameClean}`;
  }

  // Make sure ENVIRONMENT is set for Redis prefix
  if (!envContent.includes('ENVIRONMENT=')) {
    envContent += '\nENVIRONMENT=local';
  }

  // Write the updated .env file
  await fs.writeFile(path.join(worktreeDir, '.env'), envContent);
}

/**
 * Check if tmuxinator is installed
 */
async function isTmuxinatorInstalled(): Promise<boolean> {
  try {
    await execAsync('which tmuxinator');
    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Create tmuxinator configuration if available
 */
async function setupTmuxinatorConfig(
  worktreeDir: string,
  worktreeNameClean: string,
  ports: {
    portApi: number;
    portWebDebug: number;
    portCliDebug: number;
    portToolsDebug: number;
    portPatients: number;
    portDoctors: number;
    portAdmin: number;
  },
): Promise<boolean> {
  // First check if tmuxinator is installed
  if (!(await isTmuxinatorInstalled())) {
    return false;
  }

  try {
    // Check if tmuxinator config directory exists
    try {
      await fs.access(TMUXINATOR_CONFIG_DIR);
    } catch (_error) {
      // Config directory doesn't exist, so tmuxinator is probably not properly set up
      return false;
    }

    const tmuxinatorConfigPath = path.join(
      TMUXINATOR_CONFIG_DIR,
      `willow-${worktreeNameClean}.yml`,
    );

    // Create the tmuxinator config template
    const tmuxinatorContent = `name: willow-${worktreeNameClean}
root: ${worktreeDir}
pre_window: |
  export API_PORT="${ports.portApi}"
  export PORT_WEB_DEBUG="${ports.portWebDebug}"
  export PORT_CLI_DEBUG="${ports.portCliDebug}"
  export PORT_TOOLS_DEBUG="${ports.portToolsDebug}"
  export PATIENTS_PORT="${ports.portPatients}"
  export DOCTORS_PORT="${ports.portDoctors}"
  export ADMIN_PORT="${ports.portAdmin}"
  export WORKTREE_NAME="${worktreeNameClean}"
  clear
windows:
  - base:
      layout: main-vertical
      panes:
        -
  - ai:
      layout: main-vertical
      panes:
        - claude
  - api:
      layout: tiled
      panes:
        - pnpm dev:api:debug
  - patients:
      layout: tiled
      panes:
        - pp -F patients dev
  - doctors:
      layout: tiled
      panes:
        - pp -F doctors dev
  - admin:
      layout: tiled
      panes:
        - pp -F admin dev
  - services:
      layout: main-vertical
      panes:
        - pp -F api localtunnel
        - pp -F api dev:stripe
`;

    await fs.writeFile(tmuxinatorConfigPath, tmuxinatorContent);
    return true;
  } catch (_error) {
    return false;
  }
}

/**
 * Check if Docker services are running
 */
async function areDockerServicesRunning(): Promise<boolean> {
  try {
    const { stdout } = await execAsync('docker ps | grep "willow-postgres"');
    return stdout.trim().length > 0;
  } catch (_error) {
    return false;
  }
}

/**
 * Create database for the worktree
 */
async function setupDatabase(
  worktreeNameClean: string,
): Promise<{ success: boolean; reason?: string }> {
  try {
    // Create the database on the existing Postgres container
    await execAsync(
      `docker exec willow-postgres psql -U user -d willow -c "CREATE DATABASE willow_${worktreeNameClean};"`,
    );
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Check if the error is because the database already exists
    if (errorMessage.includes('already exists')) {
      console.log(
        styles.warning(
          `  Database willow_${worktreeNameClean} already exists, continuing...`,
        ),
      );
      return { success: false, reason: 'database already exists' };
    }

    // For other errors, log them but continue
    console.warn(styles.warning(`  Database creation failed: ${errorMessage}`));
    console.log(
      styles.info(
        '  Continuing with existing database or migrations will create it...',
      ),
    );
    return { success: false, reason: errorMessage };
  }
}

/**
 * Restore database from clean slate
 */
async function restoreDatabaseAndMigrate(
  worktreeDir: string,
  worktreeNameClean: string,
): Promise<void> {
  // Load environment variables from the worktree's .env file for database operations
  const envPath = path.join(worktreeDir, '.env');
  const envContent = await fs.readFile(envPath, 'utf-8');

  // Extract DATABASE_URL
  const databaseUrlMatch = /^DATABASE_URL=(.*)$/m.exec(envContent);
  const rawDatabaseUrl = databaseUrlMatch?.[1];

  if (!rawDatabaseUrl) {
    throw new Error('DATABASE_URL not found in .env file');
  }

  // Remove quotes if present
  const databaseUrl = rawDatabaseUrl.replace(/^["']|["']$/g, '');

  // Extract POSTGRES_DB
  const postgresDbMatch = /^POSTGRES_DB=(.*)$/m.exec(envContent);
  const postgresDb = postgresDbMatch?.[1] ?? `willow_${worktreeNameClean}`;

  // Run restore-database which will read the POSTGRES_DB from the worktree's .env file
  await execAsync('pnpm -F restore-database start --name=clean_slate', {
    cwd: worktreeDir,
    env: {
      ...process.env,
      DATABASE_URL: databaseUrl,
      POSTGRES_DB: postgresDb,
    },
  });

  // Then run migrations
  await execAsync('pnpm -F db migrate', {
    cwd: worktreeDir,
    env: {
      ...process.env,
      DATABASE_URL: databaseUrl,
    },
  });
}

/**
 * Install dependencies
 */
async function installDependencies(worktreeDir: string): Promise<void> {
  await execAsync('pnpm install', { cwd: worktreeDir });
}

async function main() {
  try {
    const program = new Command();

    program
      .name('worktree')
      .description(
        'Creates a new git worktree with custom tmuxinator config, environment vars, and Docker setup',
      )
      .option(
        '--name <name>',
        'Name for the worktree (max 16 characters, required with --branch)',
      )
      .option(
        '--branch <branch>',
        'Branch to checkout (creates from staging if not exists, requires --name)',
      )
      .option('--no-open', 'Skip automatically opening terminal after setup');

    program.parse();

    const options = program.opts<CommandOptions>();
    await run(options);

    process.exit(0);
  } catch (error) {
    if (
      error instanceof Error &&
      error.name !== 'ExitPromptError' &&
      error.message !== 'User interruption'
    ) {
      console.error(styles.error(error.message));
    }
    process.exit(1);
  }
}

void main();
