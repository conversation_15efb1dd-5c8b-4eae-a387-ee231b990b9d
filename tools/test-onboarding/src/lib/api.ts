import { Agent } from 'http';
import type { AxiosError, AxiosInstance, AxiosRequestConfig } from 'axios';
import axios from 'axios';
import chalk from 'chalk';

const DELAY_BETWEEN_REQUESTS = 50;

// Response type for pre-signup endpoints
interface PreSignupResponse {
  currentState: string;
  currentStep: number;
  totalSteps: number;
  canTransition: boolean;
  isComplete: boolean;
  context?: unknown;
}

// Response type after account creation (PatientSignInOutput)
interface AuthenticatedResponse {
  accessToken: string;
  refreshToken: string;
  role: string;
  status: string;
  patientId: string;
  onboarding?: {
    state: unknown;
    context: unknown;
    currentStep: number;
    totalSteps: number;
    stepName: string;
  };
  dashboard?: unknown;
  getStarted: {
    email: string;
    phone?: string;
    state?: string;
    firstName?: string;
    lastName?: string;
    gender?: string;
    birthday?: string;
  };
}

// Union type for responses
type OnboardingResponse = PreSignupResponse | AuthenticatedResponse;

// Type guard to check if response is authenticated
function _isAuthenticatedResponse(
  response: OnboardingResponse,
): response is AuthenticatedResponse {
  return 'accessToken' in response;
}

interface CreateAccountData {
  email: string;
  password: string;
  phone: string;
  getPromotionsSMS?: boolean;
}

interface NameData {
  firstName: string;
  lastName: string;
}

interface StateData {
  state: string;
}

export class OnboardingTestClient {
  protected client: AxiosInstance;
  protected cookies: string[] = [];
  private lastRequestTime = 0;
  private accessToken: string | null = null;

  constructor(
    baseUrl = process.env.API_URL ??
      `http://localhost:${process.env.API_PORT ?? '8081'}`,
  ) {
    this.client = axios.create({
      baseURL: baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 300000,
      httpAgent: new Agent({
        keepAlive: true,
        timeout: 60000,
      }),
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      withCredentials: true,
    });

    // Add request interceptor for delays and cookies
    this.client.interceptors.request.use(async (config) => {
      // Add delay if needed
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;
      if (timeSinceLastRequest < DELAY_BETWEEN_REQUESTS) {
        await new Promise((resolve) =>
          setTimeout(resolve, DELAY_BETWEEN_REQUESTS - timeSinceLastRequest),
        );
      }
      this.lastRequestTime = Date.now();

      // Add cookies to headers
      if (this.cookies.length > 0) {
        config.headers.Cookie = this.cookies.join('; ');
      }

      // Add auth token for private endpoints
      if (
        this.accessToken &&
        config.url?.includes('/onboarding/') &&
        !config.url.includes('/pre-signup/') &&
        !config.url.includes('/initialize')
      ) {
        config.headers.Authorization = `Bearer ${this.accessToken}`;
      }

      // Log request
      console.log(
        chalk.cyan('→ Request:'),
        `${config.method?.toUpperCase()} ${config.url}`,
      );
      if (config.data) {
        console.log(
          chalk.gray('  Body:'),
          JSON.stringify(config.data, null, 2),
        );
      }
      if (this.cookies.length > 0) {
        console.log(chalk.gray('  Cookies:'), this.cookies);
      }
      if (config.headers.Authorization) {
        console.log(chalk.gray('  Auth:'), 'Bearer [token]');
      }

      return config;
    });

    // Add response interceptor for cookie handling and logging
    this.client.interceptors.response.use(
      (response) => {
        // Extract and store cookies from Set-Cookie headers
        const setCookieHeaders = response.headers['set-cookie'];
        if (setCookieHeaders) {
          this.cookies = setCookieHeaders
            .map((cookie: string) => {
              // Extract just the cookie name=value part
              return cookie.split(';')[0];
            })
            .filter((cookie): cookie is string => cookie !== undefined);
        }

        // Log response
        console.log(
          chalk.green('← Response:'),
          response.status,
          response.statusText,
        );
        if (response.data) {
          console.log(
            chalk.gray('  Body:'),
            JSON.stringify(response.data, null, 2),
          );
        }
        if (setCookieHeaders) {
          console.log(chalk.gray('  Set-Cookie:'), this.cookies);
        }
        console.log(''); // Empty line for readability

        return response;
      },
      async (error: AxiosError) => {
        // Log error
        console.log(
          chalk.red('← Error:'),
          error.response?.status,
          error.response?.statusText,
        );
        if (error.response?.data) {
          console.log(
            chalk.gray('  Body:'),
            JSON.stringify(error.response.data, null, 2),
          );
        }
        console.log(''); // Empty line for readability

        const isNetworkError =
          error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT';
        const config = error.config as AxiosRequestConfig & {
          headers?: { __isRetry?: boolean };
        };

        if (isNetworkError && !config.headers?.__isRetry) {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          config.headers = { ...config.headers, __isRetry: true };
          return this.client.request(config);
        }
        throw error;
      },
    );
  }

  clearCookies(): void {
    this.cookies = [];
    console.log(chalk.yellow('Cookies cleared'));
  }

  async checkHealth(): Promise<boolean> {
    try {
      const response = await this.client.get('/healthcheck');
      return response.status === 200;
    } catch {
      return false;
    }
  }

  // Onboarding Public Controller endpoints
  async initialize(): Promise<PreSignupResponse> {
    const response = await this.client.post<PreSignupResponse>(
      '/onboarding/initialize',
    );
    return response.data;
  }

  async preSignupSetState(data: StateData): Promise<PreSignupResponse> {
    const response = await this.client.post<PreSignupResponse>(
      '/onboarding/pre-signup/set-state',
      data,
    );
    return response.data;
  }

  async preSignupName(data: NameData): Promise<PreSignupResponse> {
    const response = await this.client.post<PreSignupResponse>(
      '/onboarding/pre-signup/name',
      data,
    );
    return response.data;
  }

  async preSignupCreateAccount(
    data: CreateAccountData,
  ): Promise<AuthenticatedResponse> {
    const response = await this.client.post<AuthenticatedResponse>(
      '/onboarding/pre-signup/create-account',
      data,
    );
    // Store access token if available
    if (response.data.accessToken) {
      this.accessToken = response.data.accessToken;
    }
    return response.data;
  }

  async preSignupNext(): Promise<OnboardingResponse> {
    const response = await this.client.post<OnboardingResponse>(
      '/onboarding/pre-signup/next',
    );
    return response.data;
  }

  async preSignupBack(): Promise<OnboardingResponse> {
    const response = await this.client.post<OnboardingResponse>(
      '/onboarding/pre-signup/back',
    );
    return response.data;
  }

  // Onboarding Authenticated Controller endpoints
  async getStatus(): Promise<unknown> {
    const response = await this.client.get('/onboarding/status');
    return response.data;
  }

  async postQuestionnaire(
    event: string,
    value?: Record<string, unknown>,
  ): Promise<unknown> {
    const payload = value ? { event, value } : { event };
    const response = await this.client.post(
      '/onboarding/questionnaire',
      payload,
    );
    return response.data;
  }

  async onboardingNext(): Promise<unknown> {
    const response = await this.client.post('/onboarding/next');
    return response.data;
  }

  async onboardingBack(): Promise<unknown> {
    const response = await this.client.post('/onboarding/back');
    return response.data;
  }
}
