import { faker } from '@faker-js/faker';
import { select } from '@inquirer/prompts';
import { Command } from 'commander';
import ora from 'ora';

import { OnboardingTestClient } from './lib/api.js';
import { styles } from './lib/styles.js';

interface CommandOptions {
  state?: string;
  quick?: boolean;
  steps?: string;
}

// Common enabled states based on system analysis
const ENABLED_STATES = [
  'CA',
  'TX',
  'FL',
  'NY',
  'AZ',
  'CO',
  'GA',
  'IL',
  'NC',
  'PA',
];

process.on('SIGINT', () => {
  process.exit(0);
});

async function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function runPublicFlow(client: OnboardingTestClient, state: string) {
  console.log(styles.header('\n🚀 Starting Onboarding Public Flow Test\n'));

  // Step 1: Initialize
  let spinner = ora('Initializing onboarding...').start();
  try {
    const initResponse = await client.initialize();
    spinner.succeed(
      `Initialized - State: ${styles.value(initResponse.currentState)}, Step: ${styles.value(
        `${initResponse.currentStep}/${initResponse.totalSteps}`,
      )}`,
    );
  } catch (error) {
    spinner.fail('Failed to initialize');
    throw error;
  }

  await delay(500);

  // Step 2: Set State
  spinner = ora(`Setting state to ${state}...`).start();
  try {
    const stateResponse = await client.preSignupSetState({ state });
    spinner.succeed(
      `State set - Current: ${styles.value(stateResponse.currentState)}, Step: ${styles.value(
        `${stateResponse.currentStep}/${stateResponse.totalSteps}`,
      )}`,
    );
  } catch (error) {
    spinner.fail(`Failed to set state ${state}`);
    throw error;
  }

  await delay(500);

  // Step 3: Submit Name
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  spinner = ora(`Submitting name: ${firstName} ${lastName}...`).start();
  try {
    const nameResponse = await client.preSignupName({ firstName, lastName });
    spinner.succeed(
      `Name submitted - State: ${styles.value(nameResponse.currentState)}, Step: ${styles.value(
        `${nameResponse.currentStep}/${nameResponse.totalSteps}`,
      )}`,
    );
  } catch (error) {
    spinner.fail('Failed to submit name');
    throw error;
  }

  await delay(500);

  // Step 4: Create Account
  const email = `ramiro+${firstName.toLowerCase()}.${lastName.toLowerCase()}@startwillow.com`;
  // Generate phone number in the exact format (XXX) XXX-XXXX
  // Use valid US area codes
  const validAreaCodes = [
    201, 202, 203, 205, 206, 207, 208, 209, 210, 212, 213, 214, 215, 216, 217,
    218, 219, 224, 225, 228, 229, 231, 234, 239, 240, 248, 251, 252, 253, 254,
    256, 260, 262, 267, 269, 270, 272, 276, 281, 283, 301, 302, 303, 304, 305,
    307, 308, 309, 310, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 323,
    325, 330, 331, 334, 336, 337, 339, 346, 347, 351, 352, 360, 361, 364, 385,
    386, 401, 402, 404, 405, 406, 407, 408, 409, 410, 412, 413, 414, 415, 417,
    419, 423, 424, 425, 430, 432, 434, 435, 440, 442, 443, 458, 469, 470, 475,
    478, 479, 480, 484, 501, 502, 503, 504, 505, 507, 508, 509, 510, 512, 513,
    515, 516, 517, 518, 520, 530, 531, 534, 539, 540, 541, 551, 559, 561, 562,
    563, 564, 567, 570, 571, 573, 574, 575, 580, 585, 586, 601, 602, 603, 605,
    606, 607, 608, 609, 610, 612, 614, 615, 616, 617, 618, 619, 620, 623, 626,
    628, 629, 630, 631, 636, 641, 646, 650, 651, 657, 659, 660, 661, 662, 667,
    669, 678, 681, 682, 701, 702, 703, 704, 706, 707, 708, 712, 713, 714, 715,
    716, 717, 718, 719, 720, 724, 725, 726, 727, 731, 732, 734, 737, 740, 743,
    747, 754, 757, 760, 762, 763, 765, 769, 770, 772, 773, 774, 775, 779, 781,
    785, 786, 801, 802, 803, 804, 805, 806, 808, 810, 812, 813, 814, 815, 816,
    817, 818, 828, 830, 831, 832, 843, 845, 847, 848, 850, 854, 856, 857, 858,
    859, 860, 862, 863, 864, 865, 870, 872, 878, 901, 903, 904, 906, 907, 908,
    909, 910, 912, 913, 914, 915, 916, 917, 918, 919, 920, 925, 928, 929, 930,
    931, 936, 937, 938, 940, 941, 947, 949, 951, 952, 954, 956, 959, 970, 971,
    972, 973, 978, 979, 980, 984, 985, 989,
  ];
  const areaCode =
    validAreaCodes[Math.floor(Math.random() * validAreaCodes.length)];
  const prefix = faker.number.int({ min: 200, max: 999 });
  const lineNumber = faker.number.int({ min: 1000, max: 9999 });
  const phone = `(${areaCode}) ${prefix}-${lineNumber}`;
  const password = 'somePassword1234$';

  console.log(styles.info('\nAccount Details:'));
  console.log(`  Email: ${styles.value(email)}`);
  console.log(`  Phone: ${styles.value(phone)}`);
  console.log(`  Password: ${styles.value('[hidden]')}`);

  spinner = ora('Creating account...').start();
  try {
    const accountResponse = await client.preSignupCreateAccount({
      email,
      password,
      phone,
      getPromotionsSMS: false,
    });

    spinner.succeed('Account created successfully!');

    console.log(styles.success('\n✅ Authentication successful!'));
    console.log(
      `  Access Token: ${styles.value(accountResponse.accessToken.substring(0, 20) + '...')}`,
    );
    console.log(`  Role: ${styles.value(accountResponse.role)}`);
    console.log(`  Patient ID: ${styles.value(accountResponse.patientId)}`);
    console.log(`  Status: ${styles.value(accountResponse.status)}`);

    if (accountResponse.onboarding) {
      console.log(styles.info('\n📊 Onboarding State:'));
      console.log(
        `  Current State: ${styles.value(JSON.stringify(accountResponse.onboarding.state))}`,
      );
      console.log(
        `  Current Step: ${styles.value(`${accountResponse.onboarding.currentStep}/${accountResponse.onboarding.totalSteps}`)}`,
      );
      console.log(
        `  Step Name: ${styles.value(accountResponse.onboarding.stepName)}`,
      );

      // Show warning if state doesn't look right
      const state = accountResponse.onboarding.state as Record<string, unknown>;
      if ('preSignup' in state) {
        console.log(
          styles.error(
            '\n⚠️  WARNING: Patient is still in pre-signup state after account creation!',
          ),
        );
        console.log(
          '  This indicates the backend may not have properly transitioned to the questionnaire state.',
        );
      }
    }

    return accountResponse;
  } catch (error) {
    spinner.fail('Failed to create account');
    throw error;
  }
}

async function continueAuthenticatedFlow(
  client: OnboardingTestClient,
  accountResponse: unknown,
  steps: string | undefined,
) {
  console.log(styles.header('\n🔒 Continuing Authenticated Onboarding Flow\n'));

  // First, get the current status from the backend
  console.log(styles.info('Getting current onboarding status from backend...'));
  try {
    const status = (await client.getStatus()) as Record<string, unknown>;
    const statusState = status.state as Record<string, unknown> | undefined;
    console.log(
      `Current backend state: ${styles.value(JSON.stringify(statusState))}`,
    );
    if (statusState?.value) {
      console.log(
        `State value: ${styles.value(JSON.stringify(statusState.value))}`,
      );
    }

    // Check if the state looks like legacy format
    if (statusState && !statusState.value && !statusState.status) {
      console.log(
        styles.error(
          '\n⚠️  Backend is returning legacy onboarding state format!',
        ),
      );
      console.log(
        '  The patient record may have been created with the legacy onboarding version.',
      );
      console.log('  This is causing the state machine mismatch.');
      return;
    }
  } catch (error) {
    console.error(styles.error('Failed to get onboarding status from backend'));
    console.error(
      'This might indicate the patient record was not properly created or the onboarding state was not initialized.',
    );
    console.error('Error details:', error);
    return;
  }

  // Determine how many steps to execute
  let maxSteps = 0;
  if (steps === 'all') {
    maxSteps = 15; // All questionnaire steps
  } else if (steps) {
    maxSteps = parseInt(steps, 10);
    if (isNaN(maxSteps) || maxSteps < 1) {
      console.error(styles.error('Invalid steps value. Use a number or "all"'));
      return;
    }
  } else {
    maxSteps = 1; // Default to just the first step
  }

  console.log(
    styles.info(
      `\nExecuting ${maxSteps === 15 ? 'all' : maxSteps} questionnaire step(s)\n`,
    ),
  );

  // Track completed steps
  let completedSteps = 0;

  // Step 1: Age (birth date)
  if (completedSteps < maxSteps) {
    const birthDate = faker.date
      .birthdate({ min: 25, max: 65, mode: 'age' })
      .toISOString()
      .split('T')[0];
    const spinner = ora(
      `Step 1 - Submitting birth date: ${birthDate}...`,
    ).start();
    try {
      // First try the questionnaire endpoint
      const response = (await client.postQuestionnaire('next', {
        birthDate,
      })) as Record<string, unknown>;
      spinner.succeed(
        `Birth date submitted - Current state: ${styles.value(JSON.stringify(response.state))}`,
      );
      completedSteps++;
      await delay(500);
    } catch (error) {
      spinner.fail('Failed to submit birth date via questionnaire endpoint');
      console.log(
        styles.error(
          `Error: ${error instanceof Error ? error.message : String(error)}`,
        ),
      );

      // Try the next endpoint as an alternative
      console.log(
        styles.info(
          '\nTrying alternative approach with /onboarding/next endpoint...',
        ),
      );
      const altSpinner = ora(
        'Attempting transition via next endpoint...',
      ).start();
      try {
        const nextResponse = (await client.onboardingNext()) as Record<
          string,
          unknown
        >;
        altSpinner.succeed(
          `Transitioned - New state: ${styles.value(JSON.stringify(nextResponse))}`,
        );
      } catch (nextError) {
        altSpinner.fail('Alternative approach also failed');
        console.log(
          styles.error(
            `Next endpoint error: ${nextError instanceof Error ? nextError.message : String(nextError)}`,
          ),
        );
      }

      throw error;
    }
  }

  // Step 2: Gender
  if (completedSteps < maxSteps) {
    const gender = faker.helpers.arrayElement(['male', 'female']);
    const spinner = ora(`Step 2 - Setting gender: ${gender}...`).start();
    try {
      // For gender, the event IS the value (male or female)
      const response = (await client.postQuestionnaire(gender)) as Record<
        string,
        unknown
      >;
      spinner.succeed(
        `Gender set to ${gender} - Current state: ${styles.value(JSON.stringify(response.state))}`,
      );
      completedSteps++;
      await delay(500);
    } catch (error) {
      spinner.fail('Failed to set gender');
      throw error;
    }
  }

  // Step 3: Pregnancy check (only for females) or GLP-1 usage
  if (completedSteps < maxSteps) {
    // Check current state to determine which question we're on
    const status = (await client.getStatus()) as Record<string, unknown>;

    // Extract the current questionnaire state - handle different possible structures
    let currentQuestionnaireState: string | undefined;

    // The backend returns the state in different structures depending on the endpoint
    // From /onboarding/status it returns: { onboarding: { state: { questionnaire: "age" } } }
    // We need to handle this properly
    const onboarding = status.onboarding as Record<string, unknown> | undefined;
    if (onboarding?.state && typeof onboarding.state === 'object') {
      const obState = onboarding.state as Record<string, unknown>;
      currentQuestionnaireState = obState.questionnaire as string | undefined;
    } else if (status.state && typeof status.state === 'object') {
      const stState = status.state as Record<string, unknown>;
      currentQuestionnaireState = stState.questionnaire as string | undefined;
    }

    // Check if we're at the pregnancy question (for females) or GLP-1 usage
    if (currentQuestionnaireState === 'isPregnant') {
      const spinner = ora('Step 3 - Pregnancy check: no...').start();
      try {
        const response = (await client.postQuestionnaire('no')) as Record<
          string,
          unknown
        >;
        spinner.succeed(
          `Pregnancy check completed - Current state: ${styles.value(JSON.stringify(response.state))}`,
        );
        completedSteps++;
        await delay(500);
      } catch (error) {
        spinner.fail('Failed to complete pregnancy check');
        throw error;
      }
    } else if (currentQuestionnaireState === 'usingGLP1') {
      const usingGLP1 = faker.helpers.arrayElement(['yes', 'no']);
      const spinner = ora(`Step 3 - GLP-1 usage: ${usingGLP1}...`).start();
      try {
        const response = (await client.postQuestionnaire(usingGLP1)) as Record<
          string,
          unknown
        >;
        spinner.succeed(
          `GLP-1 usage set to ${usingGLP1} - Current state: ${styles.value(JSON.stringify(response.state))}`,
        );
        completedSteps++;
        await delay(500);
      } catch (error) {
        spinner.fail('Failed to set GLP-1 usage');
        throw error;
      }
    }
  }

  // Step 4: Have diabetes
  if (completedSteps < maxSteps) {
    const spinner = ora('Step 4 - Diabetes check: no...').start();
    try {
      const response = (await client.postQuestionnaire('no')) as Record<
        string,
        unknown
      >;
      spinner.succeed(
        `Diabetes check completed - Current state: ${styles.value(JSON.stringify(response.state))}`,
      );
      completedSteps++;
      await delay(500);
    } catch (error) {
      spinner.fail('Failed to complete diabetes check');
      throw error;
    }
  }

  // Additional steps would continue here...
  if (completedSteps < maxSteps) {
    console.log(
      styles.info(
        `\n📋 Completed ${completedSteps} steps. ${maxSteps - completedSteps} more steps available in the full questionnaire.`,
      ),
    );
  } else {
    console.log(
      styles.success(
        `\n✅ Completed all ${completedSteps} requested questionnaire steps!`,
      ),
    );
  }
}

async function run(options: CommandOptions) {
  try {
    const client = new OnboardingTestClient();

    // Clear cookies for fresh start
    client.clearCookies();

    // Check API health
    const spinner = ora('Checking API health...').start();
    try {
      const isHealthy = await client.checkHealth();
      if (!isHealthy) {
        spinner.warn('API health check failed - continuing anyway');
      } else {
        spinner.succeed('API is healthy');
      }
    } catch {
      spinner.warn('Could not check API health - continuing anyway');
    }

    // Select state
    let state = options.state;
    if (!state && !options.quick) {
      state = await select({
        message: 'Select a state to test:',
        choices: ENABLED_STATES.map((s) => ({ value: s, name: s })),
      });
    } else if (!state) {
      // Quick mode - use random state
      state = ENABLED_STATES[Math.floor(Math.random() * ENABLED_STATES.length)];
    }

    console.log(
      styles.info(`\nTesting with state: ${styles.value(state ?? '')}`),
    );

    // Run public flow
    const accountResponse = await runPublicFlow(client, state ?? 'CA');

    // Continue with authenticated flow if steps are requested
    if (
      options.steps &&
      (accountResponse as { accessToken?: string }).accessToken
    ) {
      await continueAuthenticatedFlow(client, accountResponse, options.steps);
    }

    console.log(styles.success('\n✨ Test completed successfully!'));
  } catch (error) {
    if (error instanceof Error) {
      console.error(styles.error('\n❌ Test failed:'), error.message);
      if ('response' in error) {
        const axiosError = error as { response?: { data?: unknown } };
        if (axiosError.response?.data) {
          console.error(
            styles.muted('Response data:'),
            axiosError.response.data,
          );
        }
      }
    }
    process.exit(1);
  }
}

async function main() {
  try {
    const program = new Command();

    program
      .name('test-onboarding')
      .description('Test the onboarding flow with cookie-based authentication')
      .option('--state <state>', 'State code to use (e.g., CA, TX, FL)')
      .option('--quick', 'Quick mode - skip prompts and use defaults')
      .option(
        '--steps <steps>',
        'Number of questionnaire steps to execute (e.g., 1, 2, 3, or "all")',
      );

    program.parse();

    const options = program.opts<CommandOptions>();
    await run(options);

    process.exit(0);
  } catch (error) {
    if (
      error instanceof Error &&
      error.name !== 'ExitPromptError' &&
      error.message !== 'User interruption'
    ) {
      console.error(styles.error('\nError:'), error);
    }
    process.exit(1);
  }
}

void main();
