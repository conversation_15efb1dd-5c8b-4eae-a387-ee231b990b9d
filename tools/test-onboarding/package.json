{"name": "@willow/test-onboarding", "private": true, "version": "0.0.1", "type": "module", "scripts": {"start": "pnpm --silent with-env tsx src/index.ts", "start:debug": "pnpm --silent with-env tsx --inspect-brk=${PORT_TOOLS_DEBUG:-9231} src/index.ts", "purge": "git clean -xdf .cache .turbo node_modules", "clean": "git clean -xdf .cache .turbo", "format": "prettier --check . --ignore-path ../../../.gitignore", "format:fix": "prettier --check --write . --ignore-path ../../../.gitignore", "lint": "eslint", "lint:fix": "eslint --fix", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../${ENV_FILE:-.env} --"}, "dependencies": {"@faker-js/faker": "^9.2.0", "@inquirer/prompts": "^7.0.1", "axios": "^1.7.9", "chalk": "^4.1.2", "commander": "^12.1.0", "ora": "^5.4.1", "tsx": "^4.7.1"}, "devDependencies": {"@willow/eslint-config": "workspace:*", "@willow/prettier-config": "workspace:*", "@willow/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}}